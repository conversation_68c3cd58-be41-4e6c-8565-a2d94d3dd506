@import '/cdn/res/css/_fonts.css';

/* // TODO: Add support for intl fonts */
/*
NOTO KUFI IS A TEMPORARY TEST FONT - Needs to be replaced w/
Kohinoor Arabic Medium (Indian Type Foundry)
Kohinoor Arabic Bold (Indian Type Foundry)
*/
@font-face {
    font-family: noto-kufi;
    src: url("/cdn/res/fonts/inspira/noto-kufi-arabic-regular.ttf") format("ttf"), /* Modern Browsers */
    url("/cdn/res/fonts/inspira/noto-kufi-arabic-regular.ttf") format("truetype"); /* Safari, Android, iOS */
    font-weight: normal;
    font-style: normal;
  }
  @font-face {
    font-family: noto-kufi;
    src: url("/cdn/res/fonts/inspira/noto-kufi-arabic-bold.ttf") format("ttf"), /* Modern Browsers */
    url("/cdn/res/fonts/inspira/noto-kufi-arabic-bold.ttf") format("truetype"); /* Safari, Android, iOS */
    font-weight: bold;
    font-style: normal;
  }
  
  /*
  NOTO SANS IS A TEMPORARY TEST FONT - Needs to be replaced w/
  UDJingXiHei Bold (Arphic)
  UDJingXiHei Heavy (Arphic)
  */
  @font-face {
    font-family: noto-sans;
    src: url("/cdn/res/fonts/inspira/noto-sans-regular.ttf") format("ttf"), /* Modern Browsers */
    url("/cdn/res/fonts/inspira/noto-sans-regular.ttf") format("truetype"); /* Safari, Android, iOS */
    font-weight: normal;
    font-style: normal;
  }
  @font-face {
    font-family: noto-sans;
    src: url("/cdn/res/fonts/inspira/noto-sans-bold.ttf") format("ttf"), /* Modern Browsers */
    url("/cdn/res/fonts/inspira/noto-sans-bold.ttf") format("truetype"); /* Safari, Android, iOS */
    font-weight: bold;
    font-style: normal;
  }
  
  /*
  Chinese
  UDJingXiHei Bold (Arphic)
  UDJingXiHei Heavy (Arphic)
  
  Cyrillic
  Mediator Regular (Paratype)
  Mediator Extra Bold (Paratype)
  
  Japanese
  Hiragino Kaku-Gothic ProN W3
  Hiragino Kaku-Gothic ProN W6
  
  Korean
  Nanum Gothic Regular
  Nanum Gothic Bold
  
  */