/**
 * checkbox.scss
 *
 * Copyright (c) 2019 by General Electric Company. All rights reserved.
 *
 * The copyright to the computer software herein is the property of
 * General Electric Company. The software may be used and/or copied only
 * with the written permission of General Electric Company or in accordance
 * with the terms and conditions stipulated in the agreement/contract
 * under which the software has been supplied.
 *
 * Loads checkbox variables and mixins
 *
 * @main
 * @name Checkbox
 * @usage
 * It is recommended to have your `<input type="checkbox">` be inside `<label>` and the `for` attribute
 * of the label must match the `id` attribute of the input. When grouping inputs, use the `role="group"`
 * attribute combine with aria-labelledby`.
 *
 * Standard checkbox methods are used to get and set state of the checkbox.
 *
 * ```html
 * <!--Single input-->
 * <label for="unique_id" class="checkbox u-mb--">
 *    <input id="unique_id" type="checkbox" class="checkbox__input" />
 *    <span class="checkbox__label">Option Label</span>
 * </label>
 *
 * <!--Multiple inputs-->
 * <div role="group" aria-labelledby="group-label" class="form__fieldset">
 *   <div id="group-label" class="form__label">
 *    Select Option(s)
 *   </div>
 *   <div>
 *     <div>
 *       <label for="unique_id" class="checkbox u-mb--">
 *         <input id="unique_id" type="checkbox" class="checkbox__input" />
 *         <span class="checkbox__label">Option Label</span>
 *       </label>
 *     </div>
 *     <div>
 *       <label for="unique_id1" class="checkbox u-mb--">
 *         <input id="unique_id1" type="checkbox" class="checkbox__input" />
 *         <span class="checkbox__label">Option Label</span>
 *       </label>
 *     </div>
 *   </div>
 * </div>
 * ```
 *
 * @description
 * The **checkbox** component should be used when asking the user to make a decision between two clearly
 * opposite choices or to select one or more independent items from a list of options in a form. The
 * checkbox label indicates the selected state, whereas the meaning of the cleared state must be the unambiguous opposite of the selected state.
 *
 * @browsers chrome, safari, firefox, ie11, edge
 */

@import 'node_modules/@gehc-ux/eds-core/src/app/styles/component_base';
@import "node_modules/@gehc-ux/eds-core/src/app/styles/common/breakpoints";
@import './variables';

@mixin focus-state() {
  @include next('label') {
    @include before {
      box-shadow: inset 0 0 0 $eds-checkbox-focus-border-width var(--eds-checkbox-focus-border-color);
    } // :before
    @include hover {
      @include before {
        box-shadow: inset 0 0 0 $eds-checkbox-focus-border-width var(--eds-checkbox-focus-border-color);
      }  // :before
    } // :hover

  } // + label
}

@mixin eds-checkbox {
  /**
   * @class checkbox Base class used for checkbox.
   */
  @include block('checkbox') {
    color: var(--eds-checkbox-font-color);
    cursor: pointer;
    display: inline-flex;
    font-family: $eds-base-font-family;
    font-size: $eds-checkbox-font-size;
    position: relative;
    user-select: none;
    margin-bottom: $eds-checkbox-margin-bottom;

    @media (max-width: map-get($eds-breakpoints, "small")) {
      margin-bottom: $eds-checkbox-margin-bottom-responsive;
    }

    @include after {
      clear: both;
      content: " ";
      display: block;
    } // :after

    &:empty {
      padding-left: 0;
    } // :empty

    /**
     * @class checkbox__label Class for checkbox label
     */
    @include element('label') {
      cursor: pointer;
      display: flex;
      line-height: $eds-checkbox-label-line-height;
      vertical-align: middle;
      word-break: break-all;

      @media (max-width: map-get($eds-breakpoints, "small")) {
        align-items: center;
        display: flex;
      } // small

      @include before {
        background: var(--eds-checkbox-background-color);
        border-radius: $eds-checkbox-box-border-radius;
        box-shadow: inset 0 0 0 $eds-checkbox-box-border-width var(--eds-checkbox-border-color);
        box-sizing: border-box;
        content: " ";
        display: block;
        flex: 0 0 $eds-checkbox-box-width;
        float: left;
        height: $eds-checkbox-box-height;
        margin-right: $eds-checkbox-margin-right;
        position: relative;
        top: $eds-checkbox-box-top-position;
        width: $eds-checkbox-box-width;

        @media (max-width: map-get($eds-breakpoints, "small")) {
          flex: 0 0 $eds-checkbox-box-width-responsive;
          height: $eds-checkbox-box-height-responsive;
          top: 0;
          width: $eds-checkbox-box-width-responsive;
        }

      } // :before

      @include after {
        background: var(--eds-checkbox-checked-tick-color);
        content: "";
        display: none;
        height: $eds-checkbox-box-height;
        left: $eds-checkbox-tick-position-left;
        position: absolute;
        top: $eds-checkbox-tick-position-top;

        width: $eds-checkbox-box-width;

        @media (max-width: map-get($eds-breakpoints, "small")) {
          background-size: $eds-checkbox-background-size-responsive;
          flex: 0 0 $eds-checkbox-box-width-responsive;
          height: $eds-checkbox-box-height-responsive;
          top: 0;
          width: $eds-checkbox-box-width-responsive;
        }

      } // :after

    } // .checkbox__label

    /**
     * @class checkbox__input Class for checkbox input
     */
    @include element('input') {
      height: 0;
      position: absolute;
      opacity: 0;
      width: 0;

      @include checked {
        @include next('label') {

          @include before {
            background: var(--eds-checkbox-checked-background-color);
            box-shadow: none;
          }

          @include after {
            display: block;
          } // :after

          @include at('disabled') {
            @include before {
              background: var(--eds-checkbox-disabled-checked-background-color);
              box-shadow: inset 0 0 0 $eds-checkbox-focus-border-width var(--eds-checkbox-disabled-checked-background-color);
            }
            @include after {
              background: var(--eds-checkbox-disabled-tick-color);
              @media (max-width: map-get($eds-breakpoints, "small")) {
                  background-size:$eds-checkbox-background-size-responsive;
              }
            }
          } // :disabled
        } // + label
      } // :checked

      @include focus {
        @include focus-state;
      } // :focus

      @include hover {
        @include focus-state;
      } // :hover

    } // .checkbox__input

    /**
     * @class checkbox--disabled Makes the checkbox disabled
     */
    @include modifier('disabled')  {
      pointer-events: none;

      .checkbox__label {
        color: var(--eds-checkbox-disabled-color);

        @include before {
          background: var(--eds-checkbox-disabled-background-color);
          box-shadow: inset 0 0 0 $eds-checkbox-focus-border-width var(--eds-checkbox-disabled-border-color);
        } // :before
      }
    } // .checkbox--disabled

    /**
     * @class checkbox--error Makes the checkbox error display
     */
    @include modifier('error') {
      .checkbox__label {
        @include before {
          background: var(--eds-checkbox-error-background-color);
        }
      }

      .checkbox__label,
      .checkbox__input:checked + .checkbox__label {
        @include before {
          box-shadow: inset 0 0 0 $eds-checkbox-box-border-width var(--eds-checkbox-error-border-color);
        }
      }
    }

  } // .checkbox

} // @mixin eds-checkbox

@include eds-checkbox();
