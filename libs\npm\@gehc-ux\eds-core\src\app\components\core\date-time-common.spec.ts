/**
 *  date-time-common.spec.ts
 *
 *  Copyright (c) 2018 by General Electric Company. All rights reserved.
 *
 *  The copyright to the computer software herein is the property of
 *  General Electric Company. The software may be used and/or copied only
 *  with the written permission of General Electric Company or in accordance
 *  with the terms and conditions stipulated in the agreement/contract
 *  under which the software has been supplied.
 *
 * Created by ********* on 2018-11-13.
 */

import { EDSDateTime } from './date-time-common';
import { ElementRef, ChangeDetectorRef } from '@angular/core';
import * as moment from 'moment-timezone';

describe('date-time-common', () => {
  describe('EDSDateTime constructor function', () => {
    const constructorThrowAssertions = [
      {
        missingProperty: 'valueChangedEvent',
        config: {
          format: 'mmhhA'
        },
        expectedError: '`valueChangedEvent` not provided'
      },
      {
        missingProperty: 'format',
        config: {
          valueChangedEvent: 'eds-time-picker-value-changed'
        },
        expectedError: '`format` not provided'
      }
    ];
    constructorThrowAssertions.forEach(assertion => {
      it(`should throw if ${assertion.missingProperty} is not passed in constructor`, async () => {
        try {
          const dtd = new DateTimeDerivative('eds-awesome', assertion.config);
        } catch (e) {
          expect(e.message).toContain(assertion.expectedError);
        }
      });
    });

    it('should set valueChangedEvent and format on object', async () => {
      const dtd = dateTimeDerivativeFactory();
      expect(dtd.format).toBeDefined();
      expect(dtd.valueChangedEvent).toBeDefined();
    });

    it('should set moment on EDS namespace', () => {
      const dtd = dateTimeDerivativeFactory();
      expect(window.GEHC.EDS.moment).toBeDefined();
    });
  });

  describe('EDSDateTime other functions', () => {
    let dtd;
    beforeEach(async() => {
      dtd = dateTimeDerivativeFactory();
      dtd.momentObj = moment('2042', 'HHmm');
    });

    describe('EDSDateTime selectionEditModeStart', () => {

      it('should set previousMomentObj to momentObj', () => {
        dtd.selectionEditModeStart();
        expect(dtd.previousMomentObj).toEqual(dtd.momentObj);
      });
      it('should call setupSelectControls', () => {
        spyOn(dtd, 'setupSelectControls');
        dtd.selectionEditModeStart();
        expect(dtd.setupSelectControls).toHaveBeenCalled();
      });
      it('should set previousMomentObj to null when momentObj is not set', () => {
        dtd.momentObj = null; // undo auto setting from beforeEach
        dtd.selectionEditModeStart();
        expect(dtd.previousMomentObj).toBeNull();
      });
      it('should set isElectionEditMode to true', () => {
        dtd.selectionEditModeStart();
        expect(dtd.isSelectionEditMode).toBeTruthy();
      });
    });

    describe('EDSDateTime selectionEditModeEnd', () => {
      it('should set isElectionEditMode to false', () => {
        spyOn(dtd, 'fire');
        dtd.selectionEditModeStart();
        dtd.selectionEditModeEnd();
        expect(dtd.isSelectionEditMode).toBeFalsy();
      });
      it('should call focus on trigger button', () => {
        spyOn(dtd, 'fire');
        spyOn(dtd.selectTriggerButton.nativeElement, 'focus');
        dtd.selectionEditModeStart();
        dtd.selectionEditModeEnd();
        expect(dtd.selectTriggerButton.nativeElement.focus).toHaveBeenCalled();
      });
      describe('when no cancel or cancel == false', () => {
        it('should fire with proper eventName and detail', () => {
          dtd.fire = mockFireAndAssert(dtd);
          dtd.selectionEditModeStart();
          dtd.selectionEditModeEnd();
        });
      });
      describe('when cancel == true', () => {
        it('should reset momentObj to original value', () => {
          spyOn(dtd, 'fire');
          dtd.selectionEditModeStart();
          const originalMoment = dtd.momentObj;
          dtd.momentObj = moment('1212', 'HHmm');
          dtd.selectionEditModeEnd(true);
          expect(dtd.momentObj).toEqual(originalMoment);
        });
      });
    });

    describe('EDSDateTime formatDisplay', () => {
      describe('when momentObj exists', () => {
        it('should show the formatted date value', async() => {
          dtd.format = 'hh:mm A';
          dtd.formatDisplay();
          expect(dtd.formattedDisplay).toBe('08:42 PM');
        });
      });
      describe('when no momentObj', () => {
        it('should show placeholderFormat', async() => {
          dtd.momentObj = null;
          dtd.placeHolderFormat = 'hh:mm AM';
          dtd.formatDisplay();
          expect(dtd.formattedDisplay).toBe('hh:mm AM');
        });
      });
    });

    describe('EDSDateTime inlineEditModeStart', () => {
      it('should set isInlineEditMode to true', async() => {
        dtd.inlineEditModeStart();
        expect(dtd.isInlineEditMode).toBeTruthy();
      });
      it('should call selectionEditModeEnd with cancel==true', async() => {
        spyOn(dtd, 'selectionEditModeEnd');
        dtd.inlineEditModeStart();
        expect(dtd.selectionEditModeEnd).toHaveBeenCalledWith(true);
      });
      it('should call setupInputFields', async() => {
        spyOn(dtd, 'setupInputFields');
        dtd.inlineEditModeStart();
        expect(dtd.setupInputFields).toHaveBeenCalled();
      });
      it('shoould save momentObj to previourMomentObj', async() => {
        dtd.inlineEditModeStart();
        expect(dtd.previousMomentObj).toEqual(dtd.momentObj);
      });

    });

    describe('EDSDateTime inlineEditModeEnd', () => {
      it('should set isInlineEditMode to false', async() => {
        spyOn(dtd, 'fire');  // just to fake fire and avoid downstream dependencies
        dtd.inlineEditModeEnd();
        expect(dtd.isInlineEditMode).toBeFalsy();
      });
      it('should call formatDisplay', () => {
        spyOn(dtd, 'fire'); // just to fake fire and avoid downstream dependencies
        spyOn(dtd, 'formatDisplay');
        dtd.inlineEditModeEnd();
        expect(dtd.formatDisplay).toHaveBeenCalled();
      });
      it('should call fire with valueChangedEvent', () => {
        dtd.fire = mockFireAndAssert(dtd);
        dtd.inlineEditModeEnd();
      });

    });

    describe('EDSDateTime inputContainerKeyup', () => {
      describe('when event target is the input container', () => {
        const event = { target: 'input-container' };
        describe('and Enter key is pressed', () => {
          it('should call inlineEditModeStart', () => {
            spyOn(dtd, 'inlineEditModeStart');
            dtd.inputContainerKeyup(Object.assign({}, event, {key: 'Enter'}));
            expect(dtd.inlineEditModeStart).toHaveBeenCalled();
          });
        });
        const numberKeys = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
        numberKeys.forEach(num => {
          describe(`and ${num} key is pressed`, () => {
            it(`should call inlineEditModeStart`, () => {
              spyOn(dtd, 'inlineEditModeStart');
              dtd.inputContainerKeyup(Object.assign({}, event, {key: num}));
              expect(dtd.inlineEditModeStart).toHaveBeenCalled();
            });
          });
        });
        describe('or any other key is pressed', () => {
          it('should NOT call inlineEditModeStart', () => {
            spyOn(dtd, 'inlineEditModeStart');
            dtd.inputContainerKeyup(Object.assign({}, event, {key: 'b'}));
            expect(dtd.inlineEditModeStart).not.toHaveBeenCalled();
          });
        });
      });
    });

    describe('EDSDateTime onInputEnter', () => {
      it('should call validateInputs', () => {
        spyOn(dtd, 'validateInputs');
        dtd.onInputEnter();
        expect(dtd.validateInputs).toHaveBeenCalled();
      });
    });

    describe('EDSDateTime cancelEditing', () => {
      describe('when in inline edit mode', () => {
        beforeEach(() => {
          dtd.inputContainer = {
            nativeElement: {
              focus: function() {}
            }
          };
        });
        it('should call formatDisplay()', () => {
          spyOn(dtd, 'formatDisplay');
          dtd.inlineEditModeStart();
          dtd.cancelEditing();
          expect(dtd.formatDisplay).toHaveBeenCalled();
        });
        describe('and there was a previousMomentObj', () => {
          it('should restore momentObj from previousMomentObj', () => {
            const originalMoment = dtd.momentObj;
            dtd.inlineEditModeStart();
            // change momentObj
            dtd.momentObj = moment('0908', 'HHmm');
            dtd.cancelEditing();
            expect(dtd.momentObj).toEqual(originalMoment);
          });
        });
        describe('and there was no previousMomentObj', () => {
          it('should do set momentObj to null', () => {
            dtd.momentObj = null; // undo auto set from beforeEach
            dtd.selectionEditModeStart();
            dtd.cancelEditing();
            expect(dtd.momentObj).toBeNull();
          });
        });

        it('should set isInlineEditMode to false', () => {
          dtd.inlineEditModeStart();
          dtd.cancelEditing();
          expect(dtd.isInlineEditMode).toBeFalsy();
        });
        it('should call inputContainer.nativeElement.focus()', () => {
          spyOn(dtd.inputContainer.nativeElement, 'focus');
          dtd.inlineEditModeStart();
          dtd.cancelEditing();
          expect(dtd.inputContainer.nativeElement.focus).toHaveBeenCalled();
        });

        it('should set isSelectionEditMode to fasle', () => {
          dtd.selectionEditModeStart();
          dtd.cancelEditing();
          expect(dtd.isSelectionEditMode).toBeFalsy();
        });
        it('should call selectTriggerButton.nativeElement.focus()', async() => {
          spyOn(dtd.selectTriggerButton.nativeElement, 'focus');
          dtd.selectionEditModeStart();
          dtd.cancelEditing();
          expect(dtd.selectTriggerButton.nativeElement.focus).toHaveBeenCalled();
        });
      });
    });

    describe('EDSDateTime focusInput', () => {
      describe('when focusInput is called with input field', () => {
        const input = {
          nativeElement: {
            focus: function() {},
            setSelectionRange: function() {},
            value: '10'
          }
        };
        it('should call input.nativeElement.focus()', () => {
          spyOn(input.nativeElement, 'focus');
          dtd.focusInput(input);
          expect(input.nativeElement.focus).toHaveBeenCalled();
        });
        it('should call input.nativeElement.setSelectionRange()', () => {
          spyOn(input.nativeElement, 'setSelectionRange');
          dtd.focusInput(input);
          expect(input.nativeElement.setSelectionRange).toHaveBeenCalledWith(0, 2);
        });
      });

    });

    describe('EDSDateTime clickout', () => {
      describe('when click event is outside our component', () => {
        let event;
        beforeEach(() => {
          // TODO: CODE SMELL. This test is successful, but not because event.path
          // types are corrrect. these fakes are all wrong.
          dtd.hostRef = mockElFactory();
          event = {
            path: [
              'abc',
              'def',
              'ghi'
            ]
          };
        });
      });
      /* need to figure out how to run this case to handle the 'not' case
      describe('when click event is inside our component', () => {
        it('should do nothing', () => {
          const event = {
            path: [
              'abc',
              'dev',
              'ghi',
              'eds-awesome'
            ]
          };
          spyOn(dtd, 'selectionEditModeEnd');
          spyOn(dtd, 'inlineEditModeEnd');
          dtd.clickout(event);
          expect(dtd.selectionEditModeEnd).not.toHaveBeenCalled();
          expect(dtd.inlineEditModeEnd).not.toHaveBeenCalled();

        });
      });
      */
    });
  });
});

const dateTimeDerivativeFactory = () => {
  return new DateTimeDerivative(
    'eds-awesome', {
    format: 'hhmm A',
    valueChangedEvent: 'eds-awesome-value-changed'
  });
};

class DateTimeDerivative extends EDSDateTime {
  constructor(
    componentName: string, config: any) {
    super(new ElementRef(null), componentName, config);
    // this fakes the bound @ViewChild 'selectTriggerButton'
    this.selectTriggerButton = {
      nativeElement: { focus: function() {} }
    };
    // this fakes the bound @ViewChild 'inputContainer'
    this.inputContainer = { nativeElement: 'input-container' };
  }

  changeDetector: ChangeDetectorRef;

  placeHolderFormat: string;

  setupInputFields() {}

  validateInputs() {}

  setupSelectControls() {}
}

/**
 * This provides a mock implementation of the fire function and asserts
 * the data received from the fire is valid
 * @param _dtd pass in the DateTimeDerivative instance for the given test.
 */
const mockFireAndAssert: any = (_dtd) => {
  return function(eventName, detail) {
    expect(eventName).toEqual(_dtd.valueChangedEvent);
    expect(detail.dateTime).toEqual(_dtd.momentObj);
    expect(detail.formattedValue).toEqual(_dtd.formattedDisplay);
  };
};

const mockElFactory = () => {
  return new MockElementRef();
};

class MockElementRef extends ElementRef {
  nativeElement;
  constructor() {
    super(null);
    this.nativeElement = {
      dispatchEvent: function() {}
    };
  }
}
