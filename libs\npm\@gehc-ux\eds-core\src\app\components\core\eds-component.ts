/**
 *  eds-component
 *
 *  Copyright (c) 2017 by General Electric Company. All rights reserved.
 *
 *  The copyright to the computer software herein is the property of
 *  General Electric Company. The software may be used and/or copied only
 *  with the written permission of General Electric Company or in accordance
 *  with the terms and conditions stipulated in the agreement/contract
 *  under which the software has been supplied.
 *
 *  Created by ********* on 3/5/17.
 */

import { ElementRef } from '@angular/core';
import { EDSTracking } from './eds-tracking';
declare function require(moduleName: string): any;

/**
 * extend typescript declaration to include the composed prop
 */
declare global {
  interface EventInit {
    composed?: boolean;
  }
}

export class EDSComponent extends EDSTracking {
  /**
   * native HTML element corresponding to this element
   */
  protected hostRef;

  constructor(element: ElementRef, componentName: string) {
    super(componentName);
    this.hostRef = element.nativeElement;

    window['GEHC'].EDS.theme.updateComponent(element.nativeElement);

    window.addEventListener('eds-theme-switch', () => {
      window['GEHC'].EDS.theme.updateComponent(element.nativeElement);
    });
  }

  protected preventPropagation($event) {
    if ($event.stopPropagation) {
      $event.stopPropagation();
    }
    if ($event.preventDefault) {
      $event.preventDefault();
    }
    $event.cancelBubble = true;
    $event.returnValue = false;
  }

  /**
   * @description debounce function that limits the rate at which the function is being called for performance
   */

  static debounce(func, interval, ctx?) {
    let timeout;
    return function () {

      const context = ctx ? ctx : this;
      const args = arguments;
      const later = function () {
        timeout = null;
        func.apply(context, args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, interval || 200);
    };
  }

  /**
   * @description Fires a custom event with a specific name and detail. Bubbling, composed and
   * cancellable are also configurable through parameters
   */
  fire(
    eventName: string,
    detail?: Object,
    preventBubble?: boolean,
    preventComposed?: boolean,
    cancelable?: boolean
  ) {
    let event;

    // On IE11, `CustomEvent` is not a constructor.
    if (typeof CustomEvent !== 'function') {
      event = document.createEvent('CustomEvent');
      event.initCustomEvent(name, !preventBubble, cancelable, detail);
      return event;
    } else {
      event = new CustomEvent(eventName, {
        bubbles: !preventBubble,
        cancelable: cancelable,
        composed: !preventComposed,
        detail: detail
      });
    }

    this.hostRef.dispatchEvent(event);
  }
}
