// palette.scss
// Color palette for EDS Library
// Created by 212364575 on 10/25/16
// Updated by 212368684 on 11/20/19

// Do not add multiline comment synatx on this file as it causes conflict with scss2json command

// NOTE
// If palette is modified run npm run scss2json

// WARNING
// When editing this file make SURE that you have a space between the ':' and the '#' for every var or the var will be ignored in our theming

// White swatch
$eds-white: var(--eds-white);

/* Black */
$eds-black-100: var(--eds-black-100);
$eds-black-200: var(--eds-black-200);
$eds-black-300: var(--eds-black-300);
$eds-black-400: var(--eds-black-400);
$eds-black-500: var(--eds-black-500);
$eds-black-600: var(--eds-black-600);
$eds-black-700: var(--eds-black-700);
$eds-black-800: var(--eds-black-800);
$eds-black-900: var(--eds-black-900);
$eds-black-1000: var(--eds-black-1000);
$eds-black-1100: var(--eds-black-1100);
$eds-black-1200: var(--eds-black-1200);
$eds-black-1300: var(--eds-black-1300);
$eds-black-1400: var(--eds-black-1400);
$eds-black-1500: var(--eds-black-1500);
$eds-black-1600: var(--eds-black-1600);
$eds-black-1700: var(--eds-black-1700);
$eds-black-1800: var(--eds-black-1800);
$eds-black-1900: var(--eds-black-1900);
$eds-black-2000: var(--eds-black-2000);
$eds-black: var(--eds-black);

/* Gray swatch */
$eds-gray-100: var(--eds-gray-100);
$eds-gray-200: var(--eds-gray-200);
$eds-gray-300: var(--eds-gray-300);
$eds-gray-400: var(--eds-gray-400);
$eds-gray-500: var(--eds-gray-500);
$eds-gray-600: var(--eds-gray-600);
$eds-gray-700: var(--eds-gray-700);
$eds-gray-800: var(--eds-gray-800);
$eds-gray-900: var(--eds-gray-900);
$eds-gray-1000: var(--eds-gray-1000);
$eds-gray-1100: var(--eds-gray-1100);
$eds-gray-1150: var(--eds-gray-1150);
$eds-gray-1200: var(--eds-gray-1200);
$eds-gray-1300: var(--eds-gray-1300);
$eds-gray-1400: var(--eds-gray-1400);
$eds-gray-1500: var(--eds-gray-1500);
$eds-gray-1600: var(--eds-gray-1600);

/* Blue swatch */
$eds-blue-5:  var(--eds-blue-5);
$eds-blue-25:  var(--eds-blue-25);
$eds-blue-50:  var(--eds-blue-50);
$eds-blue-75:  var(--eds-blue-75);
$eds-blue-100: var(--eds-blue-100);
$eds-blue-200: var(--eds-blue-200);
$eds-blue-225: var(--eds-blue-225);
$eds-blue-250: var(--eds-blue-250);
$eds-blue-300: var(--eds-blue-300);
$eds-blue-325: var(--eds-blue-325);
$eds-blue-350: var(--eds-blue-350);
$eds-blue-400: var(--eds-blue-400);
$eds-blue-450: var(--eds-blue-450);
$eds-blue-500: var(--eds-blue-500);
$eds-blue-600: var(--eds-blue-600);
$eds-blue-650: var(--eds-blue-650);
$eds-blue-675: var(--eds-blue-675);
$eds-blue-700: var(--eds-blue-700);
$eds-blue-750: var(--eds-blue-750);

/*Deprecated Digital Commerce color as per new specs */
$eds-dc-blue-400: var(--eds-dc-blue-400);
$eds-dc-blue-750: var(--eds-dc-blue-750);

/*Digital Commerce Base swatch */
$eds-dc-base-100: var(--eds-dc-base-100);
$eds-dc-base-150: var(--eds-dc-base-150);
$eds-dc-base-300: var(--eds-dc-base-300);
$eds-dc-base-350: var(--eds-dc-base-350);
$eds-dc-base-475: var(--eds-dc-base-475);
$eds-dc-base-500: var(--eds-dc-base-500);
$eds-dc-base-600: var(--eds-dc-base-600);
$eds-dc-base-700: var(--eds-dc-base-700);
$eds-dc-base-750: var(--eds-dc-base-750);
$eds-dc-base-800: var(--eds-dc-base-800);
$eds-dc-base-900: var(--eds-dc-base-900);

/*Digital Commerce Action swatch */
$eds-dc-blue-200: var(--eds-dc-blue-200);
$eds-dc-blue-250: var(--eds-dc-blue-250);
$eds-dc-blue-300: var(--eds-dc-blue-300);
$eds-dc-blue-350: var(--eds-dc-blue-350);
$eds-dc-blue-600: var(--eds-dc-blue-600);
$eds-dc-blue-600: var(--eds-dc-blue-600); 
$eds-dc-blue-700: var(--eds-dc-blue-700);

/*Digital Commerce product segment swatch */
$eds-dc-purple-500: var(--eds-dc-purple-500);
$eds-dc-purple-650: var(--eds-dc-purple-650);
$eds-dc-purple-700: var(--eds-dc-purple-700);
$eds-dc-purple-1000: var(--eds-dc-purple-1000);

/*AI swatch */
$eds-purple-100: var(--eds-purple-100);
$eds-purple-300: var(--eds-purple-300);
$eds-purple-700: var(--eds-purple-700);

/* Red swatch */
$eds-red-100: var(--eds-red-100);
$eds-red-105: var(--eds-red-105);
$eds-red-115: var(--eds-red-115);
$eds-red-150: var(--eds-red-150);
$eds-red-200: var(--eds-red-200);
$eds-red-210: var(--eds-red-210);
$eds-red-220: var(--eds-red-220);
$eds-red-250: var(--eds-red-250);
$eds-red-300: var(--eds-red-300);

/* Orange swatch */
$eds-orange-100: var(--eds-orange-100);
$eds-orange-125: var(--eds-orange-125);
$eds-orange-150: var(--eds-orange-150);
$eds-orange-200: var(--eds-orange-200);
$eds-orange-400: var(--eds-orange-400);
$eds-orange-600: var(--eds-orange-600);

/* Yellow swatch */
$eds-yellow-125: var(--eds-yellow-125);
$eds-yellow-150: var(--eds-yellow-150);
$eds-yellow-200: var(--eds-yellow-200);
$eds-yellow-400: var(--eds-yellow-400);
$eds-yellow-600: var(--eds-yellow-600);
$eds-yellow-700: var(--eds-yellow-700);

/* Green swatch */
$eds-green-75:var(--eds-green-75);
$eds-green-100:var(--eds-green-100);
$eds-green-150:var(--eds-green-150);
$eds-green-200:var(--eds-green-200);
$eds-green-300:var(--eds-green-300);
$eds-green-500:var(--eds-green-500);

/* Teal swatch */
$eds-teal-100: var(--eds-teal-100);
$eds-teal-150: var(--eds-teal-150);
$eds-teal-400: var(--eds-teal-400);
$eds-teal-500: var(--eds-teal-500);

/* Brown swatch */
$eds-brown-150: var(--eds-brown-150);
$eds-brown-250: var(--eds-brown-250);
$eds-brown-300: var(--eds-brown-300);

/* Sand swatch */
$eds-sand-150: var(--eds-sand-150);
$eds-sand-400: var(--eds-sand-400);
$eds-sand-500: var(--eds-sand-500);

/* Slate swatch */
$eds-slate-150: var(--eds-slate-150);
$eds-slate-300: var(--eds-slate-300);
$eds-slate-400: var(--eds-slate-400);

/* Amber swatch */
$eds-amber-100: var(--eds-amber-100);
$eds-amber-200: var(--eds-amber-200);

/* Bronze swatch */
$eds-bronze-150: var(--eds-bronze-150);
$eds-bronze-300: var(--eds-bronze-300);
$eds-bronze-400: var(--eds-bronze-400);

/* Coral swatch */
$eds-coral-150: var(--eds-coral-150);
$eds-coral-300: var(--eds-coral-300);
$eds-coral-400: var(--eds-coral-400);

/* Olive swatch */
$eds-olive-150: var(--eds-olive-150);
$eds-olive-300: var(--eds-olive-300);
$eds-olive-400: var(--eds-olive-400);

/* Violet swatch */
$eds-violet-150: var(--eds-violet-150);
$eds-violet-300: var(--eds-violet-300);
$eds-violet-400: var(--eds-violet-400);

/* Ocean swatch */
$eds-ocean-150: var(--eds-ocean-150);
$eds-ocean-300: var(--eds-ocean-300);
$eds-ocean-400: var(--eds-ocean-400);

/* Plum swatch */
$eds-plum-150: var(--eds-plum-150);
$eds-plum-300: var(--eds-plum-300);
$eds-plum-400: var(--eds-plum-400);

/* TODO: Revisit blue + marketing color */
$eds-blue-marketing-commerce-300: var(--eds-blue-marketing-commerce-300);

/* Extended Qualitative Colors (suffix with ec) */

$eds-pink-ec-100: var(--eds-pink-ec-100);
$eds-pink-ec-500: var(--eds-pink-ec-500);
$eds-pink-ec-900: var(--eds-pink-ec-900);
$eds-pink-ec-1000: var(--eds-pink-ec-1000);

$eds-aqua-ec-100: var(--eds-aqua-ec-100);
$eds-aqua-ec-500: var(--eds-aqua-ec-500);
$eds-aqua-ec-900: var(--eds-aqua-ec-900);

$eds-peach-ec-100: var(--eds-peach-ec-100);
$eds-peach-ec-500: var(--eds-peach-ec-500);
$eds-peach-ec-900: var(--eds-peach-ec-900);

$eds-violet-ec-100: var(--eds-violet-ec-100);
$eds-violet-ec-500: var(--eds-violet-ec-500);
$eds-violet-ec-900: var(--eds-violet-ec-900);

$eds-emerald-ec-100: var(--eds-emerald-ec-100);
$eds-emerald-ec-500: var(--eds-emerald-ec-500);
$eds-emerald-ec-900: var(--eds-emerald-ec-900);

$eds-cyan-ec-100: var(--eds-cyan-ec-100);
$eds-cyan-ec-500: var(--eds-cyan-ec-500);
$eds-cyan-ec-900: var(--eds-cyan-ec-900);

$eds-amber-ec-100: var(--eds-amber-ec-100);
$eds-amber-ec-500: var(--eds-amber-ec-500);
$eds-amber-ec-900: var(--eds-amber-ec-900);

$eds-indigo-ec-100: var(--eds-indigo-ec-100);
$eds-indigo-ec-500: var(--eds-indigo-ec-500);
$eds-indigo-ec-900: var(--eds-indigo-ec-900);

$eds-olive-ec-100: var(--eds-olive-ec-100);
$eds-olive-ec-500: var(--eds-olive-ec-500);
$eds-olive-ec-900: var(--eds-olive-ec-900);

$eds-purple-ec-100: var(--eds-purple-ec-100);
$eds-purple-ec-500: var(--eds-purple-ec-500);
$eds-purple-ec-900: var(--eds-purple-ec-900);

$eds-rose-ec-100: var(--eds-rose-ec-100);
$eds-rose-ec-500: var(--eds-rose-ec-500);
$eds-rose-ec-900: var(--eds-rose-ec-900);

$eds-teal-ec-100: var(--eds-teal-ec-100);
$eds-teal-ec-500: var(--eds-teal-ec-500);
$eds-teal-ec-900: var(--eds-teal-ec-900);

$eds-plum-ec-100: var(--eds-plum-ec-100);
$eds-plum-ec-500: var(--eds-plum-ec-500);
$eds-plum-ec-900: var(--eds-plum-ec-900);

$eds-orange-ec-100: var(--eds-orange-ec-100);
$eds-orange-ec-500: var(--eds-orange-ec-500);
$eds-orange-ec-900: var(--eds-orange-ec-900);

$eds-sky-ec-100: var(--eds-sky-ec-100);
$eds-sky-ec-500: var(--eds-sky-ec-500);
$eds-sky-ec-900: var(--eds-sky-ec-900);