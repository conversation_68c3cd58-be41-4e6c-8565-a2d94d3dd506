/**
 *  _mixins.scss
 *
 *
 *
 *  Created by 212364575 on 3/7/17.
 */

@mixin placeholder ($enable-pseudo-class: true) {
  &:-moz-placeholder{
    opacity: 1; // Firefox fix
    @content;
  }
  &::-moz-placeholder{
    opacity: 1; // Firefox fix
    @content;
  }
  &::-webkit-input-placeholder {
    @content;
  }
  &:-ms-input-placeholder {
    @content;
  }
  @if($enable-pseudo-class){
    &:placeholder-shown{
      @content;
    }
  }

}
