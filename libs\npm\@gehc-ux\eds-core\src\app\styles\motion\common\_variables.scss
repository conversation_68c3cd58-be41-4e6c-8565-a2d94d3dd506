/**
*  _variables.scss
*
*
*
*/

/* 
* ANIMATION TYPES - ELEMENT
* Animation or transition
*/
  $animate: 'animate';
  $transition: 'transition';
  
/* 
* BASIC ANIMATIONS - MODIFIER
* Choose a basic animation as a modifier
*/
  $fade: 'fade';
  $slide: 'slide';

/* 
* DURATIONS
* Locked, do not change without checking with design
*/

  $eds-speed-50ms: 50ms;
  $eds-speed-100ms: 100ms;
  $eds-speed-200ms: 200ms;
  $eds-speed-300ms: 300ms;
  $eds-speed-400ms: 400ms;
  $eds-speed-500ms: 500ms;
  $eds-speed-600ms: 600ms;

  $speed-map: (
    speed-50:  $eds-speed-50ms, 
    speed-100: $eds-speed-100ms, 
    speed-200: $eds-speed-200ms, 
    speed-300: $eds-speed-300ms, 
    speed-400: $eds-speed-400ms, 
    speed-500: $eds-speed-500ms, 
    speed-600: $eds-speed-600ms, 
  );

/* 
* CURVES 
* Locked, do not change without checking with design. All animations have this mapped to the correct easing.
* Enter/In: Used for components entering into the screen.
* Exit/Out: Used for components exiting into the screen.
* Standard: Used for components not changing state (in any fashion, opacity, height...) but is used to move components around on the screen.
*/

  $standard-curve: cubic-bezier(0.47, 0, 0.745, 0.715);
  $enter-curve: cubic-bezier(0.215, 0.61, 0.355, 1);
  $exit-curve: cubic-bezier(0.55, 0.055, 0.675, 0.19);

  $curve-map: (
    // For stateless animation
    standard: $standard-curve, 
    // For when components OPEN OR ENTER in/in to view
    open:  $enter-curve, 
    enter: $enter-curve, 
    // For when components EXIT OR CLOSE out/out of view
    close: $exit-curve, 
    exit:  $exit-curve,
  );

/* 
* FILL MODES
* Choose a fill mode, forwards is a default
*/

  $single-play: forwards;
  $loop: infinite;

/* 
* ANIMATION MAPS
*/

$slide-y-map: (
  down-level1: 5px,
  down-level2: 25px,
  down-level3: 50px,
  down-level4: 75px,
  down-level5: 100px,
  up-level1: -5px,
  up-level2: -25px,
  up-level3: -50px,
  up-level4: -75px,
  up-level5: -100px,
);

$slide-x-map: (
  right-level1: 5px,
  right-level2: 25px,
  right-level3: 50px,
  right-level4: 75px,
  right-level5: 100px,
  left-level1: -5px,
  left-level2: -25px,
  left-level3: -50px,
  left-level4: -75px,
  left-level5: -100px,
);

/* 
* PRESS STATE
* Transform mixin for pressed state
*/

$pressed-state: matrix(0.98, 0, 0, 0.98, 0, 0);

