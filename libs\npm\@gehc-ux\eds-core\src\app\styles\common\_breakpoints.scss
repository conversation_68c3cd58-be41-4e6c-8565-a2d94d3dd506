@import './breakpoint-definitions';

// General Breakpoints
// Usage: @media (min-width: map-get($eds-breakpoints, "large")) { /* Sass here */ }
$eds-breakpoints: (
  'base': $eds-breakpoint-base,
  'smallest': $eds-breakpoint-smallest,
  'small': $eds-breakpoint-small,
  'medium': $eds-breakpoint-medium,
  'large': $eds-breakpoint-large,
  'xlarge': $eds-breakpoint-xlarge,
  'xxlarge': $eds-breakpoint-xxlarge
);

// Phone-specific Breakpoints
// Usage: @media (min-width: map-get($eds-phone-media-breakpoints, "landscape-medium")) { /* Sass here */ }
$eds-phone-media-breakpoints: (
  'landscape-medium': $eds-breakpoint-landscape-medium,
  'landscape-large': $eds-breakpoint-landscape-large,
  'portrait-medium': $eds-breakpoint-portrait-medium,
  'portrait-large':  $eds-breakpoint-portrait-large
);

// Tablet-specifc Breakpoints
// Usage: @media (min-width: map-get($eds-tablet-media-breakpoints, "landscape-large")) { /* Sass here */ }
$eds-tablet-media-breakpoints: (
  'landscape-xlarge': $eds-breakpoint-landscape-xlarge,
  'portrait-xlarge':  $eds-breakpoint-portrait-xlarge
);
