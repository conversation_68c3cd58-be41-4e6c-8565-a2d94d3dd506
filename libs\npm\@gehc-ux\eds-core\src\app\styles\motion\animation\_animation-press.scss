/**
*  _pressed.scss
*
*
*
*/

@import '../common/variables';
@import '../common/speed';
@import '../transition/transition';

/**
* PRESSED STATE
* PRESSED is a sudo active transition class that transitions a transform matrix.
* Use this transition on elements that you want to mimick being pressed. 
**/

    @include block('#{$animate}') {
      @include element('press') {
        @include trans(all, speed-200, standard);
        transform: $pressed-state;
      }
    }







