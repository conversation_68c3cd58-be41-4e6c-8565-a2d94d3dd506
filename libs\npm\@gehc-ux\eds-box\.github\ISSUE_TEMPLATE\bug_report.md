---
name: EDS Box Bug report
about: Create a report to help improve the design system

---

**Describe the bug**
- Component being used, parameters passed [e.g (`variable: {object: object, key: key}`)], slots passed, snippet of code, and clear concise details
- [e.g. When using select component in my React environment, I pass the following parameters ```items: [{
        "value": 1,
        "label": "Option 1"
    },
    {
        "value": 2,
        "label": "Option 2",
        "disabled": true
    }]``` I would like to disable `Option 2`, but it is still allowed to be interacted with]

**EDS Version**
- EDS: [e.g. cat node_modules/@gehc-ux/eds/package.json]
- Components: [e.g. window.GEHC.EDS.components]

**Application Environment**
- [e.g. Node: v10.11.0, Angular 7.0.0]

**URL Site** (if applicable)
- [e.g https://eds.health.ge.com/action-button]

**To Reproduce**
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

**Expected behavior**
- Output you expected

**Screenshots**
- If applicable, add screenshots to help explain your problem.

**Desktop (please complete the following information):**
 - OS: [e.g. Windows]
 - Browser [e.g. chrome, safari]
 - Version [e.g. 22]

**Smartphone (please complete the following information):**
 - Device: [e.g. iPhone6]
 - OS: [e.g. iOS8.1, Android Oreo]
 - Browser [e.g. chrome, safari]
 - Version [e.g. 22]

**Additional context**
- Add any other context about the problem here.
