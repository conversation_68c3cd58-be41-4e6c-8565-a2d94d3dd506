/**
 * box.scss
 *
 * Copyright (c) 2017 by General Electric Company. All rights reserved.
 *
 * The copyright to the computer software herein is the property of
 * General Electric Company. The software may be used and/or copied only
 * with the written permission of General Electric Company or in accordance
 * with the terms and conditions stipulated in the agreement/contract
 * under which the software has been supplied.
 *
 * Loads box variables and mixin
 *
 * @main
 * @name Box Layout
 * @browsers chrome, safari, firefox, ie11, edge
 * @usage
 * A box can be built of div by using the various classes provided by this module. Adding the `box` class to div will style it as a box. It can then be further decorated
 * in size as shown below.
 *
 * Here is the list of available colors for border and background colors classes:
 *    eds-white, gray-100, gray-200, gray-300, gray-400, gray-500,  gray-600,  gray-700,  gray-800,  gray-900,
 *    gray-1000,  gray-1100,  gray-1200,  gray-1300,  gray-1400,  gray-1500,  gray-1600,  gray-1700,  gray-1800,
 *    gray-1900, gray-2000,  gray-2100, eds-black
 *
 * If you want gray-500 for box background color, class would be: 'box--bg-gray-500'.
 *
 * If you want eds-black for box border color, class would be: 'box--border-eds-black'.
 *
 * ```html
 * <!-- Default -->
 * <div class="box">...</div>
 *
 * <!-- Box Flush -->
 * <div class="box box--flush">...</div>
 *
 * <!-- Box Tiny -->
 * <div class="box box--tiny">...</div>
 *
 * <!-- Box Small -->
 * <div class="box box--small">...</div>
 *
 * <!-- Box Large -->
 * <div class="box box--large">...</div>
 *
 * <!-- Box Huge -->
 * <div class="box box--huge">...</div>
 * ```
 *
 * @description
 * The **box layout** component is a utility with a series of spacing helper classes used to box off content in the UI layout and define spacing through a variety of padding options.
 */

@import 'node_modules/@gehc-ux/eds-core/src/app/styles/component_base';
@import 'node_modules/@gehc-ux/eds-core/src/app/styles/common/named_colors';
@import 'mixins';
@import 'variables';

@mixin eds-box {

  /**
   * @class box Class to render boxes with normal gutters
   */
  @include block( 'box' ) {
    background-color: var(--eds-box-default-background-color);
    border: 1px solid var(--eds-box-border-color);
    border-radius: 1px;
    box-sizing: border-box;
    color: var(--eds-box-default-text-color);
    display: block;
    line-height: 1;
    padding: $eds-box-spacing-medium;
    width: 100%; // this is for when we're using box inside flex

    p:first-child {
      margin-top: 0;
    }

    /**
     * @class box--min-height Class to add a min-height
     */
    @include modifier( 'min-height' ) {
      min-height: $eds-box-min-height;
    }

    /**
     * @class box--position-relative Setting the box position to relative, useful in combination with elevation settings
     */
    @include modifier( 'position-relative' ) {
      position: relative;
    }

    /**
     * @class box--flush Class to render boxes with no gutters
     */
    @include modifier( 'flush' ) {
      padding: 0;
    }

    /**
     * @class box--tiny Class to render boxes with tiny gutters
     */
    @include modifier( 'tiny' ) {
      padding: $eds-box-spacing-tiny;
    }

    /**
     * @class box--small Class to render boxes with small gutters
     */
    @include modifier( 'small' ) {
      padding: $eds-box-spacing-small;
    }

    /**
     * @class box--large Class to render boxes with large gutters
     */
    @include modifier( 'large' ) {
      padding: $eds-box-spacing-large;
    }

    /**
     * @class box--huge Class to render boxes with huge gutters
     */
    @include modifier( 'huge' ) {
      padding: $eds-box-spacing-huge;
    }

    /**
     * @class box--bg-[color-name] Class to render different background colors for a box module
     */
    @each $name, $color in $color-map-grays {
      @include modifier( 'bg-#{$name}' ) {
         background-color: #{$color};
      }
    }

    /**
     * @class box--bg-none Class to remove the default background color
     */
    @include modifier( 'bg-none' ) {
      background: transparent;
    }

    /**
     * @class box--radius-none Class to remove the default background color
     */
    @include modifier( 'radius-none' ) {
      border-radius: 0;
    }

    /**
     * @class box--border-[color-name] Class to render different border colors for a box module
     */
    @each $name, $color in $color-map-grays {
      @include modifier( 'border-#{$name}' ) {
         border-color: #{$color};
      }
    }

    /**
     * @class box--border-none Class to remove the default border
     */
    @include modifier( 'border-none' ) {
      border: 0;
    }

    /**
     * @class box--elevation-1dp Add single-level elevation to a box
     */
    @include modifier('elevation-1dp') {
      @include elevation-1dp;

      @include hover {
        @include elevation-1dp;
      }

      @include focus {
        @include elevation-1dp;
      }

    }

  /**
   * @class box--elevation-2dp Add secondary depth elevation to a box
   */
  @include modifier('elevation-2dp') {
    @include elevation-2dp;

    @include hover {
      @include elevation-2dp;
    }

    @include focus {
      @include elevation-2dp;
    }

  }

    /**
     * @class box--elevation-12dp Add multiple-level elevation to a box
     */
    @include modifier('elevation-12dp') {
      @include elevation-12dp;

      @include hover {
        @include elevation-12dp;
      }

      @include focus {
        @include elevation-12dp;
      }

    }

    /**
     * @class box--fix-top Fix a box to the top of the browser
     */
    @include modifier('fix-top') {
      bottom: auto; // just in case a bottom value is set
      left: 0;
      min-height: $eds-box-min-size-fixed;
      position: fixed;
      top: 0;
      width: 100%;
      z-index: 500;
    }

    /**
     * @class box--fix-right Fix a box to the right of the browser
     */
    @include modifier('fix-right') {
      height: 100%;
      left: auto; // just in case a right value is set
      min-height: 0;
      width: $eds-box-min-size-fixed;
      position: fixed;
      right: 0;
      top: 0;
      z-index: 500;
    }

    /**
     * @class box--fix-bottom Fix a box to the bottom of the browser
     */
    @include modifier('fix-bottom') {
      bottom: 0;
      left: 0;
      height: $eds-box-min-size-fixed;
      position: fixed;
      top: auto;  // just in case a top value is set
      width: 100%;
      z-index: 500;
    }

    /**
     * @class box--fix-left Fix a box to the left of the browser
     */
    @include modifier('fix-left') {
      height: 100%;
      left: 0;
      min-height: 0;
      width: $eds-box-min-size-fixed;
      position: fixed;
      right: auto; // just in case a right value is set
      top: 0;
      z-index: 500;
    }

    /**
     * @class bix--float-left Fix a box to the left of a given area
     */
    @include modifier('float-left') {
      height: 100%;
      left: 0;
      min-height: 0;
      width: $eds-box-min-size-fixed;
      position: absolute;
      right: auto; // just in case a right value is set
      top: 0;
    }

    /**
     * @class bix--float-right Fix a box to the right of a given area
     */
    @include modifier('float-right') {
      height: 100%;
      left: auto; // just in case a left value is set
      min-height: 0;
      width: $eds-box-min-size-fixed;
      position: absolute;
      right: 0; // just in case a right value is set
      top: 0;
    }

    /**
     * @class box--float-bottom Fix a box to the bottom of a given area
     */
    @include modifier('float-bottom') {
      bottom: 0;
      left: 0;
      height: $eds-box-min-size-fixed;
      position: absolute;
      top: auto;  // just in case a top value is set
      width: 100%;
    }

     /**
      * @class box--float-top Fix a box to the top of a given area
      */
     @include modifier('float-top') {
       bottom: auto; // just in case a bottom value is set
       left: 0;
       height: $eds-box-min-size-fixed;
       position: absolute;
       top: 0;
       width: 100%;
     }

  } // block('box')

} // eds-widths()

 @include eds-box();
