/**
 * accordion.scss
 *
 * Copyright (c) 2017 by General Electric Company. All rights reserved.
 *
 * The copyright to the computer software herein is the property of
 * General Electric Company. The software may be used and/or copied only
 * with the written permission of General Electric Company or in accordance
 * with the terms and conditions stipulated in the agreement/contract
 * under which the software has been supplied.
 *
 * @main
 * @name Accordion Styles
 * @usage
 *
 * ##### Accordion Heading
 * The accordion heading is a button element that opens and closes the panel under it. It contains three important attributes: `id`, `aria-expanded`, and `aria-controls`. The `id` value a unique identifier for the element, it also connects programmatically with the accordion panel for assistive technology. The `aria-expanded` attribute speaks to the state of the panel that is controlled by trigger button. If the panel is open, this value should be set to `true`, if it is closed, this value should be set to `false`. The `aria-controls` attribute tells assistive technology the `id` value of what this button is controlling (it must match the `id` value on the associated panel).
 *
 * The accordion heading is also wrapped in a `dt` element, this element has an attribute of `role="heading"` and `aria-level="3"` - this means it is programmatically equal to using a thrid level heading. You should update this value based on the HTML hierachy of your application.
 *
 * ##### Accordion Panel
 * The accordion panel contains the content within the accordion section. It has four important attributes to note: `id`, `role`, `aria-labelledby`, and `aria-hidden`. The `id` value is used to connect the trigger button's `aria-controls`, these values should match. The `role` attribute flags this area as important to assistive technology. The `aria-labelledby` attribute connects labelling for the panel to the content of the trigger button, giving more semantic meaning to this area. The `aria-hidden` attribute signifies the open/close state of the panel. If the panel is open `aria-hidden` should be set to `false`, if the panel is closed `aria-hidden` should be set to `true`.
 *
 * ```html
 *  <dl role="presentation">
 *    <div class="accordion">
 *      <dt aria-level="3" role="heading">
 *        <button aria-controls="accordionpanel1" aria-expanded="true" class="accordion__heading flex flex--middle" id="accordionheader1">
 *          <div class="accordion__chevron">
 *            <svg data-name="Layer 1" height="10" id="Layer_1" viewBox="0 0 10 10" width="10" xmlns="http://www.w3.org/2000/svg">
 *              <g data-name="Caret - Down - 12" id="Caret_-_Down_-_12">
 *                <path d="M5,7.5a.47.47,0,0,1-.35-.15L.15,2.85a.48.48,0,0,1,0-.7.48.48,0,0,1,.7,0L5,6.29,9.15,2.15a.49.49,0,0,1,.7.7l-4.5,4.5A.47.47,0,0,1,5,7.5Z"></path>
 *              </g>
 *            </svg>
 *          </div>
 *          <div class="accordion__trigger text-bold-16" type="button"> Group title </div>
 *        </button>
 *      </dt>
 *      <dd aria-hidden="true" aria-labelledby="accordion1id" class="accordion__panel opened" id="accordionpanel1" role="region">
 *        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Amet risus nullam eget felis. Consequat interdum varius sit amet mattis vulputate enim nulla aliquet. Bibendum at varius vel pharetra vel turpis nunc eget lorem. Est ullamcorper eget nulla facilisi. Accumsan lacus vel facilisis volutpat est velit egestas dui. Sed risus ultricies tristique nulla aliquet.</p>
 *      </dd>
 *    </div>
 *    <div class="accordion">
 *      <dt aria-level="3" role="heading">
 *        <button aria-controls="accordionpanel1" aria-expanded="true" class="accordion__heading flex flex--middle" id="accordionheader1">
 *          <div class="accordion__chevron">
 *            <svg data-name="Layer 1" height="10" id="Layer_1" viewBox="0 0 10 10" width="10" xmlns="http://www.w3.org/2000/svg">
 *              <g data-name="Caret - Down - 12" id="Caret_-_Down_-_12">
 *                <path d="M5,7.5a.47.47,0,0,1-.35-.15L.15,2.85a.48.48,0,0,1,0-.7.48.48,0,0,1,.7,0L5,6.29,9.15,2.15a.49.49,0,0,1,.7.7l-4.5,4.5A.47.47,0,0,1,5,7.5Z"></path>
 *              </g>
 *            </svg>
 *          </div>
 *          <div class="accordion__trigger text-bold-16" type="button"> Group title </div>
 *        </button>
 *      </dt>
 *      <dd aria-hidden="true" aria-labelledby="accordion1id" class="accordion__panel hidden" id="accordionpanel1" role="region">
 *        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Amet risus nullam eget felis. Consequat interdum varius sit amet mattis vulputate enim nulla aliquet. Bibendum at varius vel pharetra vel turpis nunc eget lorem. Est ullamcorper eget nulla facilisi. Accumsan lacus vel facilisis volutpat est velit egestas dui. Sed risus ultricies tristique nulla aliquet.</p>
 *      </dd>
 *    </div>
 *    <div class="accordion">
 *      <dt aria-level="3" role="heading">
 *        <button aria-controls="accordionpanel1" aria-expanded="true" class="accordion__heading flex flex--middle" id="accordionheader1">
 *          <div class="accordion__chevron">
 *            <svg data-name="Layer 1" height="10" id="Layer_1" viewBox="0 0 10 10" width="10" xmlns="http://www.w3.org/2000/svg">
 *              <g data-name="Caret - Down - 12" id="Caret_-_Down_-_12">
 *                <path d="M5,7.5a.47.47,0,0,1-.35-.15L.15,2.85a.48.48,0,0,1,0-.7.48.48,0,0,1,.7,0L5,6.29,9.15,2.15a.49.49,0,0,1,.7.7l-4.5,4.5A.47.47,0,0,1,5,7.5Z"></path>
 *              </g>
 *            </svg>
 *          </div>
 *          <div class="accordion__trigger text-bold-16" type="button"> Group title </div>
 *        </button>
 *      </dt>
 *      <dd aria-hidden="true" aria-labelledby="accordion1id" class="accordion__panel hidden" id="accordionpanel1" role="region">
 *        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Amet risus nullam eget felis. Consequat interdum varius sit amet mattis vulputate enim nulla aliquet. Bibendum at varius vel pharetra vel turpis nunc eget lorem. Est ullamcorper eget nulla facilisi. Accumsan lacus vel facilisis volutpat est velit egestas dui. Sed risus ultricies tristique nulla aliquet.</p>
 *      </dd>
 *    </div>
 *  </dl>
 * ```
 *
 * @description
 * The accordion component provides expandable and collapsible subsection headers for a page
 *
 */

@import 'node_modules/@gehc-ux/eds-core/src/app/styles/component_base';
@import 'node_modules/@gehc-ux/eds-core/src/app/styles/common/breakpoints';
@import 'node_modules/@gehc-ux/eds-flexbox/scss/flexbox';
@import 'node_modules/@gehc-ux/eds-typography/scss/typography';
@import 'node_modules/@gehc-ux/eds-spacing/scss/spacing';
@import 'node_modules/@gehc-ux/eds-spacing-responsive/scss/spacing-responsive';
@import 'node_modules/@gehc-ux/eds-widths/scss/widths';
@import 'node_modules/@gehc-ux/eds-widths-responsive/scss/widths-responsive';
@import 'variables';

@mixin eds-accordion {

  /**
   * @class accordion overall wrapping element for the component
   */
  @include block('accordion') {
    border-bottom: 1px solid var(--eds-accordion-border-color);
    margin: 0;
    -webkit-font-smoothing: antialiased;

    &.filled { border-bottom: 6px solid transparent; }

   /**
    * @class accordion__heading apply styling to an accordion header row (includes titles, icons, and additional text)
    */
    @include element('heading') {
      appearance: none;
      background-color: transparent;
      border: 1px solid transparent;
      padding: $eds-accordion-header-padding;
      width: 100%;

      &.filled { background-color: var(--eds-accordion-filled-header-background); }

      &:hover,
      &:focus {
        border-color: var(--eds-accordion-focus-border-color);
      }

      /**
        * @class accordion__chevron apply styling to the caret icon
        */
      @include element('chevron') {
        margin-right: $eds-accordion-trigger-horizontal-margin;
        color: var(--eds-accordion-text-color);

        @include modifier('right') {
          margin-left: $eds-accordion-trigger-horizontal-margin;
          margin-right: 0;
          order: 6;
        }

        @include modifier('opened') {
          transform: rotate(180deg);
        }
      }

      /**
        * @class accordion__icon apply styling to the header icon
        */
      @include element('icon') {
        margin-right: $eds-accordion-trigger-horizontal-margin;
        color: var(--eds-accordion-text-color);
        fill: var(--eds-accordion-text-color);

        @include modifier('right') {
          margin-left: $eds-accordion-trigger-horizontal-margin;
          margin-right: 0;
          order: 5;
        }

        @include modifier('hidden') { display: none; }
      }

      /**
        * @class accordion__trigger apply styling to the accordion header's title
        */
      @include element('trigger') {
        background-color: transparent;
        border-width: 0px;
        color: var(--eds-accordion-text-color);
        flex-grow: 2;
        padding: 0;
        text-align: left;
      }

      /**
      * @class accordion__additional-text apply styling to accordion header's additional text
      */
      @include element('additional-text') {
        color: var(--eds-accordion-additional-text-color);
        flex-grow: 2;
        font-family: $eds-base-font-family;
        font-size: $eds-accordion-additional-text-font-size;
        text-align: right;
        line-height: $eds-accordion-additional-text-line-height;
      }
    }

   /**
    * @class accordion__panel apply styling to an accordion item panel
    */
    @include element('panel') {
      color: var(--eds-accordion-text-color);
      height: auto;
      margin: 0;
      overflow: hidden;
      padding: $eds-accordion-panel-padding-top $eds-accordion-panel-padding-right $eds-accordion-panel-padding-bottom $eds-accordion-panel-padding-left;

      @include modifier('hidden') {
        padding-top: 0;
        padding-bottom: 0;
        max-height: 0;
      }

      @include modifier('header-icon-padding') { padding-left: $eds-accordion-header-icon-panel-left-padding; }
      @include modifier('caret-on-right') { padding-left: $eds-accordion-caret-right-panel-left-padding; }
    }
  }

} // eds-accordion()

@include eds-accordion();
