$eds-badge-border-radius: calculateRem(6px);
$eds-badge-font-size: calculateRem(14px);
$eds-badge-letter-spacing: .05em;
$eds-badge-height-small: calculateRem(20px);
$eds-badge-height-medium: calculateRem(26px);
$eds-badge-height-large: calculateRem(30px);
$eds-badge-count-size: calculateRem(20px);
$eds-badge-count-size-small: calculateRem(12px);
$eds-badge-count-size-large: calculateRem(24px);
$eds-badge-count-min-width: calculateRem(0px);
$eds-badge-count-border-width: calculateRem(2px);
$eds-badge-count-font-size: $eds-medium-font-size;
$eds-badge-count-font-size-large: $eds-large-medium-font-size;
$eds-badge-count-line-height-large: calculateRem(22px);
$eds-badge-icon-margin-right: calculateRem(4px);
$eds-badge-icon-margin-left: calculateRem(-3px);
$eds-badge-icon-margin-left-small: calculateRem(0px);
$eds-badge-icon-margin-left-large: calculateRem(-1px);
$eds-badge-icon-margin-left-small-flag: calculateRem(-4px);
$eds-badge-padding-small: calculateRem(6px);
$eds-badge-padding: calculateRem(8px);
$eds-badge-padding-none: calculateRem(0px);
$eds-badge-flag-width: calculateRem(15px);
$eds-badge-notification-count-padding: calculateRem(4px);
$eds-badge-notification-count-radius: calculateRem(15px);
$eds-badge-notification-icon-size: calculateRem(12px);

$eds-badge-flag-padding-right: calculateRem(0px);
$eds-badge-flag-border-radius: calculateRem(0px);

$eds-badge-notification-icon-margin: calculateRem(0px);
$eds-badge-notification-padding: $eds-badge-padding-none;
$eds-badge-notification-border-radius: calculateRem(0px);

$eds-badge-options-dark-text: (
  'severity-high-alt',
  'severity-moderate',
  'severity-moderate-alt-a',
  'severity-low',
  'severity-low-alt-a',
  'success',
  'info-1',
  'info-2',
  'info-3',
  'info-4',
  'info-5',
);

$eds-badge-options-light-text: (
  'severity-high',
  'info-6',
  'severity-moderate-alt-b',
  'success-alt',
  'severity-low-alt-b',
  'info-1-alt',
  'info-2-alt',
  'info-3-alt',
  'info-4-alt',
  'info-5-alt',
);
