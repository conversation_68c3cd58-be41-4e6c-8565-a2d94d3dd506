/**
 *  property-typing.ts
 *
 *  Copyright (c) 2019 by General Electric Company. All rights reserved.
 *
 *  The copyright to the computer software herein is the property of
 *  General Electric Company. The software may be used and/or copied only
 *  with the written permission of General Electric Company or in accordance
 *  with the terms and conditions stipulated in the agreement/contract
 *  under which the software has been supplied.
 *
 */
import { EDSComponent } from './eds-component';
import { createCustomElement } from '@angular/elements';
import { ComponentNgElementStrategy, ComponentNgElementStrategyFactory } from '@angular/elements/esm5/src/component-factory-strategy'
import { Injector } from '@angular/core';

const META_NAME = '__meta__input__types__';

/**
 * Decorator allowing to give a runtime type to an Input(). This type will then be used
 * to deserialize the attribute to the property
 * @param type string describing the type that will be used to deserialize the
 * attribute to the property
 *  - object
 *  - array
 *  - boolean
 *  - number
 *  - string
 * @param callback optional callback to override deserialization. If provided this will
 * be used instead of relying on the type provided by `type`. The function takes the 
 * attribute value received in and needs to return the deserialized value
 */
export function PropType(type: string, callback?: Function) {

  return function(ngClass: EDSComponent, inputName: string) {

    const ctor = ngClass.constructor,
          meta = ctor[META_NAME] ? ctor[META_NAME] : {};

    meta[inputName] = {
      'type': type,
      'callback': callback
    };
    ctor[META_NAME] = meta;
  }
}

/**
 * \@description Creates a custom element class based on an Angular component.
 *
 * Builds a class that encapsulates the functionality of the provided component and
 * uses the configuration information to provide more context to the class.
 * Takes the component factory's inputs and outputs to convert them to the proper
 * custom element API and add hooks to input changes.
 *
 * The configuration's injector is the initial injector set on the class,
 * and used by default for each created instance.This behavior can be overridden with the
 * static property to affect all newly created instances, or as a constructor argument for
 * one-off creations.
 * 
 * This EDS override allows us to add deserialization from attributes to properties
 * based on type provided with `PropType()` decorator on components Inputs
 *
 * \@publicApi
 * @template P
 * @param {?} component The component to transform.
 * @param {?} config A configuration that provides initialization information to the created class.
 * @return {?} The custom-element construction class, which can be registered with
 * a browser's `CustomElementRegistry`.
 *
 */
export function edsCreateCustomElement(component, config) {

  config.strategyFactory = config.strategyFactory || new EDSElementStrategyFactory(component, config.injector);
  let customElementClass = createCustomElement(component, config);

  // extend the attributeChangedCallback to use our deserialization
  const attrCallback = customElementClass.prototype.attributeChangedCallback,
        customCallback = function(attrName, oldValue, newValue, namespace) {

          // add same check already present in attributeChangedCallback since
          // we also use ngElementStrategy
          if (!this.ngElementStrategy) {
              this.ngElementStrategy = config.strategyFactory.create(config.injector);
          }

          newValue = this.ngElementStrategy.deserialize(attrName, newValue);
          attrCallback.call(this, attrName, oldValue, newValue, namespace);
        }

  customElementClass.prototype.attributeChangedCallback = customCallback;
  
  return customElementClass;
}


/**
 * Custom strategy factory, just wraps the angular one to create our strategy
 */
export class EDSElementStrategyFactory extends ComponentNgElementStrategyFactory {

  component;
  injector;
  componentFactory;

  constructor(component, injector) {
    super(component, injector);
  }

  /** Creates a new instance to be used for an NgElement. */
  create(injector: Injector): EDSElementStrategy {

    return new EDSElementStrategy(this.componentFactory, injector);
  }
}

/**
 * Custom angular => element strategy, allowing us to handle deserialization. Extends
 * the one provided by angular with deserializations function which will be called
 * by the custom element wrapper created in `edsCreateCustomElement`
 */
export class EDSElementStrategy extends ComponentNgElementStrategy {

  componentFactory;

  constructor(componentFactory, injector) {
    super(componentFactory, injector);
  }

  /**
   * Deserializes a property value (usually gotten from an attribute as a string)
   * into its expected type (gotten from metadata on the component)
   * @param prop 
   * @param value 
   */
  deserialize(prop, value) {

    // fetch types for all inputs from constructor
    const expectedType = this.componentFactory.componentType[META_NAME],
          propNameCamelCase = prop.replace(/-([a-z])/g, function (g) { return g[1].toUpperCase(); });
   
    if (expectedType && expectedType[propNameCamelCase]) {

      if (expectedType[propNameCamelCase].callback) {

        // if someone passed a custom deserialization ignore ours
        value = expectedType[propNameCamelCase].callback(value);
      } else {
        value = this.deserializeType(expectedType[propNameCamelCase].type, value);
      }
    }

    return value;
  }

  /**
   * Deserialize a value into an expected type
   * @param expected type defined by `PropType()` decorator
   * @param value the value to deserialize
   */
  deserializeType(expectedType, value) {

    let newValue = value;
    const valueType = typeof value;

    switch (expectedType) {
      case 'object':
      case 'array':
        newValue = this.deserializeComplexTypes(value, valueType);
        break;
      case 'string':
        // need to toString() object? should never happen
        break;
      case 'number':
          newValue = this.deserializeNumber(value, valueType);
        break;
      case 'boolean':
        newValue = this.deserializeBoolean(value, valueType);
        break;
    }

    return newValue;
  }

  /**
   * Deserialize the value into a number
   * @param value 
   * @param type 
   */
  deserializeNumber(value, type) {
    if (type !== 'number') {
      value = parseFloat(value);
    }

    return value;
  }

  /**
   * Deserialize the value into an object/array
   * @param value 
   * @param type 
   */
  deserializeComplexTypes(value, type) {
    if (type !== 'object') {
      value = JSON.parse(value);
    }

    return value;
  }

  /**
   * Deserialize the value into a bool
   * @param value 
   * @param type 
   */
  deserializeBoolean(value, type) {

    if (type !== 'boolean') {
      if (type === 'string') {
        value = value === 'false' ? false : true;
      } else {
        value = !!value;
      }
    }

    return value;
  }


}