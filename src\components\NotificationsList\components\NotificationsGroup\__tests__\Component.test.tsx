import { render, screen, fireEvent } from '@testing-library/react';
import { NotificationsGroup } from '../Component';
import { NotificationsGroupProps, ItemsGroup } from '../models';
import { ServiceNotification, NotificationObjectType } from '../../NotificationCardV2/models';

// Mock dependencies
jest.mock('cx-dle-component-library', () => ({
  CardContainer: ({ children }: { children: any }) => (
    <div data-testid="card-container">{children}</div>
  ),
}));

jest.mock('../../NotificationCardV2/Component', () => ({
  NotificationCardV2: ({ 
    item, 
    buttonText, 
    selectButtonHandler, 
    warrantyExpiredText,
    onWatchMessageText,
    contractExpiredText,
    overduePMText,
    onHoldMessageText,
    dateFormat 
  }: any) => (
    <div 
      data-testid="notification-card-v2" 
      data-notification-id={item.notificationId}
      data-button-text={buttonText}
      data-warranty-expired-text={warrantyExpiredText}
      data-on-watch-message-text={onWatchMessageText}
      data-contract-expired-text={contractExpiredText}
      data-overdue-pm-text={overduePMText}
      data-on-hold-message-text={onHoldMessageText}
      data-date-format={dateFormat}
    >
      <span>{item.subject}</span>
      <button 
        data-testid="mock-select-button" 
        onClick={(e) => selectButtonHandler(e)}
      >
        {buttonText}
      </button>
    </div>
  ),
}));

describe('NotificationsGroup Component', () => {
  const mockSelectRequestAsset = jest.fn();

  const baseServiceNotification: ServiceNotification = {
    assetRowId: 'asset-123',
    read: false,
    subject: 'Test Notification 1',
    orderTimestamp: '2023-01-01T10:00:00Z',
    notificationId: 'notification-123',
    user: {
      id: 'user-123',
      firstname: 'John',
      lastname: 'Doe',
      email: '<EMAIL>',
      locale: 'en-us',
    },
    eventData: {
      eventId: 'event-123',
      eventTimestamp: '2023-01-01T08:00:00Z',
      physicalLocationName: 'Main Hospital',
      physicalLocationCity: 'New York',
      physicalLocationRegion: 'NY',
      rowId: 'event-row-123',
    },
    creationTimestamp: '2023-01-01T10:00:00Z',
    objectRowId: 'object-123',
    objectType: NotificationObjectType.ServiceRequest,
    modality: {
      id: 'CT',
      abbreviation: 'CT',
      description: 'Computed Tomography',
      preferred: true,
      isActive: true,
      modalityDisplayName: 'CT Scanner',
    },
    productDescription: 'CT Scanner Model XYZ',
    equipmentId: 'EQ-12345',
    engineerTypeCode: 'ENG01',
    serviceStateMessageCode: 'MSG01',
    serviceStateGeneratedMessage: 'Service in progress',
    sfdcNotificationFlag: {
      code: 'FLAG01',
      title: 'Service Request',
      icon: {
        iconName: 'service-icon',
        iconPrefix: 'ico',
        iconBackground: 'primary',
      },
    },
    requester: {
      fullName: 'Jane Smith',
    },
    contractExpirationDate: '2024-12-31',
    warrantyExpirationDate: '2024-06-30',
    endOfServiceLife: '2025-12-31',
  };

  const secondServiceNotification: ServiceNotification = {
    ...baseServiceNotification,
    notificationId: 'notification-456',
    subject: 'Test Notification 2',
    objectRowId: 'object-456',
    eventData: {
      ...baseServiceNotification.eventData!,
      eventId: 'event-456',
      rowId: 'event-row-456',
    },
  };

  const mockGroup: ItemsGroup<ServiceNotification> = {
    title: 'Today',
    items: [baseServiceNotification, secondServiceNotification],
  };

  const baseProps: NotificationsGroupProps = {
    group: mockGroup,
    selectRequestAsset: mockSelectRequestAsset,
    buttonText: 'View Details',
    warrantyExpiredText: 'Warranty Expired',
    onWatchMessageText: 'On Watch',
    contractExpiredText: 'Contract Expired',
    overduePMText: 'Overdue PM',
    onHoldMessageText: 'On Hold',
    dateFormat: 'MM/DD/YYYY',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic Rendering', () => {
    it('renders without crashing', () => {
      render(<NotificationsGroup {...baseProps} />);
      expect(screen.getByText('Today')).toBeInTheDocument();
    });

    it('renders the group title', () => {
      render(<NotificationsGroup {...baseProps} />);
      expect(screen.getByText('Today')).toBeInTheDocument();
      expect(screen.getByText('Today')).toHaveClass('ge-notifications-group__time');
    });

    it('renders CardContainer wrapper', () => {
      render(<NotificationsGroup {...baseProps} />);
      expect(screen.getByTestId('card-container')).toBeInTheDocument();
    });

    it('renders the notifications list', () => {
      const { container } = render(<NotificationsGroup {...baseProps} />);
      const list = container.querySelector('.ge-notifications-group__list');
      expect(list).toBeInTheDocument();
      expect(list?.tagName).toBe('UL');
    });

    it('renders correct number of notification items', () => {
      render(<NotificationsGroup {...baseProps} />);
      const notificationCards = screen.getAllByTestId('notification-card-v2');
      expect(notificationCards).toHaveLength(2);
    });

    it('renders each notification as a list item', () => {
      const { container } = render(<NotificationsGroup {...baseProps} />);
      const listItems = container.querySelectorAll('.ge-notifications-group__list-item');
      expect(listItems).toHaveLength(2);
      listItems.forEach(item => {
        expect(item.tagName).toBe('LI');
      });
    });
  });

  describe('Notification Cards', () => {
    it('passes correct props to NotificationCardV2 components', () => {
      render(<NotificationsGroup {...baseProps} />);
      
      const notificationCards = screen.getAllByTestId('notification-card-v2');
      
      notificationCards.forEach(card => {
        expect(card).toHaveAttribute('data-button-text', 'View Details');
        expect(card).toHaveAttribute('data-warranty-expired-text', 'Warranty Expired');
        expect(card).toHaveAttribute('data-on-watch-message-text', 'On Watch');
        expect(card).toHaveAttribute('data-contract-expired-text', 'Contract Expired');
        expect(card).toHaveAttribute('data-overdue-pm-text', 'Overdue PM');
        expect(card).toHaveAttribute('data-on-hold-message-text', 'On Hold');
        expect(card).toHaveAttribute('data-date-format', 'MM/DD/YYYY');
      });
    });

    it('renders notification subjects correctly', () => {
      render(<NotificationsGroup {...baseProps} />);
      expect(screen.getByText('Test Notification 1')).toBeInTheDocument();
      expect(screen.getByText('Test Notification 2')).toBeInTheDocument();
    });

    it('generates unique keys for list items', () => {
      const { container } = render(<NotificationsGroup {...baseProps} />);
      const listItems = container.querySelectorAll('.ge-notifications-group__list-item');
      
      // Check that each list item has a unique key (React will use the key for the element)
      expect(listItems[0]).toBeInTheDocument();
      expect(listItems[1]).toBeInTheDocument();
      
      // Verify the notification IDs are different
      const cards = screen.getAllByTestId('notification-card-v2');
      expect(cards[0]).toHaveAttribute('data-notification-id', 'notification-123');
      expect(cards[1]).toHaveAttribute('data-notification-id', 'notification-456');
    });
  });

  describe('Select Request Asset Functionality', () => {
    it('calls selectRequestAsset with eventData.rowId when available', () => {
      render(<NotificationsGroup {...baseProps} />);
      
      const selectButtons = screen.getAllByTestId('mock-select-button');
      fireEvent.click(selectButtons[0]);
      
      expect(mockSelectRequestAsset).toHaveBeenCalledWith('event-row-123', expect.any(Object));
    });

    it('calls selectRequestAsset with objectRowId when eventData.rowId is not available', () => {
      const groupWithoutEventRowId: ItemsGroup<ServiceNotification> = {
        title: 'Today',
        items: [{
          ...baseServiceNotification,
          eventData: {
            ...baseServiceNotification.eventData!,
            rowId: undefined,
          },
        }],
      };

      const propsWithoutEventRowId = {
        ...baseProps,
        group: groupWithoutEventRowId,
      };

      render(<NotificationsGroup {...propsWithoutEventRowId} />);
      
      const selectButton = screen.getByTestId('mock-select-button');
      fireEvent.click(selectButton);
      
      expect(mockSelectRequestAsset).toHaveBeenCalledWith('object-123', expect.any(Object));
    });

    it('calls selectRequestAsset with objectRowId when eventData is null', () => {
      const groupWithoutEventData: ItemsGroup<ServiceNotification> = {
        title: 'Today',
        items: [{
          ...baseServiceNotification,
          eventData: undefined,
        }],
      };

      const propsWithoutEventData = {
        ...baseProps,
        group: groupWithoutEventData,
      };

      render(<NotificationsGroup {...propsWithoutEventData} />);
      
      const selectButton = screen.getByTestId('mock-select-button');
      fireEvent.click(selectButton);
      
      expect(mockSelectRequestAsset).toHaveBeenCalledWith('object-123', expect.any(Object));
    });
  });

  describe('Edge Cases', () => {
    it('renders empty group correctly', () => {
      const emptyGroup: ItemsGroup<ServiceNotification> = {
        title: 'Empty Group',
        items: [],
      };

      const emptyProps = {
        ...baseProps,
        group: emptyGroup,
      };

      render(<NotificationsGroup {...emptyProps} />);
      
      expect(screen.getByText('Empty Group')).toBeInTheDocument();
      expect(screen.queryByTestId('notification-card-v2')).not.toBeInTheDocument();
      
      const { container } = render(<NotificationsGroup {...emptyProps} />);
      const listItems = container.querySelectorAll('.ge-notifications-group__list-item');
      expect(listItems).toHaveLength(0);
    });

    it('handles single notification in group', () => {
      const singleItemGroup: ItemsGroup<ServiceNotification> = {
        title: 'Single Item',
        items: [baseServiceNotification],
      };

      const singleItemProps = {
        ...baseProps,
        group: singleItemGroup,
      };

      render(<NotificationsGroup {...singleItemProps} />);
      
      expect(screen.getByText('Single Item')).toBeInTheDocument();
      expect(screen.getAllByTestId('notification-card-v2')).toHaveLength(1);
      expect(screen.getByText('Test Notification 1')).toBeInTheDocument();
    });

    it('handles long group titles', () => {
      const longTitleGroup: ItemsGroup<ServiceNotification> = {
        title: 'This is a very long group title that might wrap to multiple lines',
        items: [baseServiceNotification],
      };

      const longTitleProps = {
        ...baseProps,
        group: longTitleGroup,
      };

      render(<NotificationsGroup {...longTitleProps} />);
      
      expect(screen.getByText('This is a very long group title that might wrap to multiple lines')).toBeInTheDocument();
    });
  });

  describe('Component Structure', () => {
    it('renders with correct CSS classes', () => {
      const { container } = render(<NotificationsGroup {...baseProps} />);
      
      expect(container.querySelector('.ge-notifications-group')).toBeInTheDocument();
      expect(container.querySelector('.ge-notifications-group__time')).toBeInTheDocument();
      expect(container.querySelector('.ge-notifications-group__list')).toBeInTheDocument();
      expect(container.querySelectorAll('.ge-notifications-group__list-item')).toHaveLength(2);
    });

    it('maintains proper HTML structure', () => {
      const { container } = render(<NotificationsGroup {...baseProps} />);

      const mainDiv = container.querySelector('.ge-notifications-group');
      const timeDiv = container.querySelector('.ge-notifications-group__time');
      const cardContainer = container.querySelector('[data-testid="card-container"]');
      const list = container.querySelector('.ge-notifications-group__list');

      expect(mainDiv).toBeInTheDocument();
      expect(timeDiv).toBeInTheDocument();
      expect(cardContainer).toBeInTheDocument();
      expect(list).toBeInTheDocument();

      // Verify the structure by checking parent-child relationships
      expect(mainDiv?.contains(timeDiv)).toBe(true);
      expect(mainDiv?.contains(cardContainer)).toBe(true);
      expect(cardContainer?.contains(list)).toBe(true);
    });
  });
});
