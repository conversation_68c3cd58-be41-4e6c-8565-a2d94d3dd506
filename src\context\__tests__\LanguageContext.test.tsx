import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { LanguageProvider, useLanguage } from '../LanguageContext';

// Mock dependencies
jest.mock('react-i18next', () => ({
  useTranslation: jest.fn(() => ({
    i18n: {
      loadLanguages: jest.fn().mockResolvedValue(undefined),
      changeLanguage: jest.fn().mockResolvedValue(undefined),
    },
  })),
}));

jest.mock('../../utils/localeMapper', () => ({
  getLocaleFromHostname: jest.fn(() => 'en-us'),
}));

jest.mock('../../hooks', () => ({
  useValidateParams: jest.fn(() => ({ lang: undefined })),
}));

jest.mock('../../config', () => ({
  validLocales: ['en-us', 'pt-br', 'fr-fr', 'es-pa', 'de-de'],
}));

// Test component that uses the context
const TestComponent: React.FC = () => {
  const { language, setLanguage } = useLanguage();

  return (
    <div>
      <div data-testid="current-language">{language || 'loading'}</div>
      <button
        data-testid="change-language-btn"
        onClick={() => setLanguage('pt-br')}
      >
        Change Language
      </button>
    </div>
  );
};

// Component to test hook outside provider
const TestComponentOutsideProvider: React.FC = () => {
  try {
    useLanguage();
    return <div>Should not render</div>;
  } catch (error) {
    return <div data-testid="error-message">{(error as Error).message}</div>;
  }
};

describe('LanguageContext', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('useLanguage hook', () => {
    it('throws error when used outside LanguageProvider', () => {
      render(<TestComponentOutsideProvider />);

      expect(screen.getByTestId('error-message')).toHaveTextContent(
        'useLanguage must be used within a LanguageProvider'
      );
    });
  });

  describe('LanguageProvider', () => {
    it('renders children and provides context', async () => {
      render(
        <LanguageProvider>
          <TestComponent />
        </LanguageProvider>
      );

      await waitFor(() => {
        expect(screen.getByTestId('current-language')).toBeInTheDocument();
      });
    });

    it('provides language context functionality', async () => {
      render(
        <LanguageProvider>
          <TestComponent />
        </LanguageProvider>
      );

      // Wait for component to render
      await waitFor(() => {
        expect(screen.getByTestId('current-language')).toBeInTheDocument();
      });

      // Should have change language button
      expect(screen.getByTestId('change-language-btn')).toBeInTheDocument();
    });
  });
});
