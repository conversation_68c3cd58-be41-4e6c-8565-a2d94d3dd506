import { useQuery } from '@tanstack/react-query';
import { frontendApi } from '../../services';
import { useLanguage } from '../../context';

export const usePageData = <T>(pageType: string, queryKeyPrefix: string) => {
  const { language } = useLanguage();

  return useQuery<T, Error, T, [string, string]>({
    queryKey: [queryKeyPrefix, language],
    queryFn: () => frontendApi.fetchPageData<T>(pageType, 'en-us'),
    retry: false,
    staleTime: 5 * 60 * 1000, // 5 minutes Caching
    enabled: !!language, // Enable the query only once language is available
  });
};

export const useCommonPageData = <T>(pageType: string, queryKeyPrefix: string) => {
  const { language } = useLanguage();

  return useQuery<T, Error, T, [string, string]>({
    queryKey: [queryKeyPrefix, language],
    queryFn: () => frontendApi.fetchDleCommonData<T>(pageType, language),
    retry: false,
    staleTime: 5 * 60 * 1000, // 5 minutes Caching
    enabled: !!language, // Enable the query only once language is available
  });
};
