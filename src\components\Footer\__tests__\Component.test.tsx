import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import Footer from '../index';
import { FooterFields } from '../models';

// Mock the useFooter hook
jest.mock('../../../hooks', () => ({
  useFooter: jest.fn(),
}));

// Mock the RedesignFooter component
jest.mock('cx-dle-component-library/components/RedesignFooter', () => ({
  RedesignFooter: jest.fn(({ fields, componentName }) => (
    <div data-testid="redesign-footer">
      <div data-testid="footer-component-name">{componentName}</div>
      <div data-testid="footer-disclaimer">{fields?.disclaimer?.value}</div>
      <div data-testid="footer-copyright">{fields?.copyright?.value}</div>
      <div data-testid="footer-company-name">{fields?.companyName?.value}</div>
      {fields?.legalLinks?.map((link: any, index: number) => (
        <div key={index} data-testid={`legal-link-${index}`}>
          {link.fields?.link?.value?.text}
        </div>
      ))}
      {fields?.utilityLinks?.map((link: any, index: number) => (
        <div key={index} data-testid={`utility-link-${index}`}>
          {link.fields?.ctaLink?.value?.text}
        </div>
      ))}
      {fields?.contactLinks?.map((link: any, index: number) => (
        <div key={index} data-testid={`contact-link-${index}`}>
          {link.fields?.title?.value}
        </div>
      ))}
      {fields?.socialMediaLinks?.map((link: any, index: number) => (
        <div key={index} data-testid={`social-link-${index}`}>
          {link.fields?.link?.value?.text}
        </div>
      ))}
    </div>
  )),
}));

const mockFooterData: FooterFields = {
  id: 'footer-1',
  name: 'Main Footer',
  disclaimer: {
    value: 'This is a disclaimer text for the footer.'
  },
  redesignFooter: {
    value: true
  },
  footerCDNLogo: {
    value: 'https://example.com/logo.png'
  },
  legalLinks: [
    {
      id: 'legal-1',
      isAvailableInPackage: 'true',
      itemUrl: '/legal/privacy',
      fields: {
        id: 'legal-1',
        name: 'Privacy Policy',
        link: {
          value: {
            href: '/privacy',
            text: 'Privacy Policy',
            target: '_self',
            anchor: '',
            linktype: 'internal'
          }
        },
        icon: {
          value: {
            src: '/icons/privacy.svg',
            alt: 'Privacy Icon',
            width: '24',
            height: '24'
          }
        }
      }
    },
    {
      id: 'legal-2',
      isAvailableInPackage: 'true',
      itemUrl: '/legal/terms',
      fields: {
        id: 'legal-2',
        name: 'Terms of Service',
        link: {
          value: {
            href: '/terms',
            text: 'Terms of Service',
            target: '_self',
            anchor: '',
            linktype: 'internal'
          }
        },
        icon: {
          value: {
            src: '/icons/terms.svg',
            alt: 'Terms Icon',
            width: '24',
            height: '24'
          }
        }
      }
    }
  ],
  copyright: {
    value: '© 2024 GE HealthCare. All rights reserved.'
  },
  accountUtilityLinks: [],
  footerLink: {
    value: {
      href: '/',
      text: 'Home',
      target: '_self',
      anchor: '',
      linktype: 'internal'
    }
  },
  footerLogo: {
    value: {
      src: '/logo.png',
      alt: 'Company Logo',
      width: '120',
      height: '40'
    }
  },
  languageLocationSelector: {
    value: {
      href: '/language-selector',
      text: 'Language & Location',
      target: '_self',
      anchor: '',
      linktype: 'internal'
    }
  },
  utilityLinks: [
    {
      id: 'utility-1',
      isAvailableInPackage: 'true',
      itemUrl: '/support',
      fields: {
        id: 'utility-1',
        name: 'Support',
        ctaLink: {
          value: {
            href: '/support',
            text: 'Support',
            target: '_self',
            anchor: '',
            linktype: 'internal'
          }
        }
      }
    }
  ],
  contactLinks: [
    {
      id: 'contact-1',
      isAvailableInPackage: 'true',
      itemUrl: '/contact',
      fields: {
        id: 'contact-1',
        name: 'Contact Us',
        title: {
          value: 'Contact Us'
        },
        link: {
          value: {
            href: '/contact',
            text: 'Contact',
            target: '_self',
            anchor: '',
            linktype: 'internal'
          }
        }
      }
    }
  ],
  socialMediaLinks: [
    {
      id: 'social-1',
      isAvailableInPackage: 'true',
      itemUrl: '/social/twitter',
      fields: {
        id: 'social-1',
        name: 'Twitter',
        link: {
          value: {
            href: 'https://twitter.com/company',
            text: 'Twitter',
            target: '_blank',
            anchor: '',
            linktype: 'external'
          }
        },
        iconClass: {
          value: 'ico-twitter'
        },
        icon: {
          value: {
            src: '/icons/twitter.svg',
            alt: 'Twitter Icon',
            width: '24',
            height: '24'
          }
        }
      }
    }
  ],
  cookieText: {
    value: 'We use cookies to improve your experience.'
  },
  companyName: {
    value: 'GE HealthCare'
  },
  contacts: {
    value: 'Contact Information'
  },
  people: {
    value: 'Our People'
  },
  address: {
    value: '123 Main Street, City, State 12345'
  }
};

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('Footer Component', () => {
  const mockUseFooter = require('../../../hooks').useFooter;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders footer when data is available and redesignFooter is enabled', () => {
    mockUseFooter.mockReturnValue({
      footerData: mockFooterData,
      error: null,
    });

    render(<Footer />, { wrapper: createWrapper() });

    expect(screen.getByTestId('redesign-footer')).toBeInTheDocument();
    expect(screen.getByTestId('footer-component-name')).toHaveTextContent('HeaderFooter');
    expect(screen.getByTestId('footer-disclaimer')).toHaveTextContent('This is a disclaimer text for the footer.');
    expect(screen.getByTestId('footer-copyright')).toHaveTextContent('© 2024 GE HealthCare. All rights reserved.');
    expect(screen.getByTestId('footer-company-name')).toHaveTextContent('GE HealthCare');
  });

  it('renders legal links correctly', () => {
    mockUseFooter.mockReturnValue({
      footerData: mockFooterData,
      error: null,
    });

    render(<Footer />, { wrapper: createWrapper() });

    expect(screen.getByTestId('legal-link-0')).toHaveTextContent('Privacy Policy');
    expect(screen.getByTestId('legal-link-1')).toHaveTextContent('Terms of Service');
  });

  it('renders utility links correctly', () => {
    mockUseFooter.mockReturnValue({
      footerData: mockFooterData,
      error: null,
    });

    render(<Footer />, { wrapper: createWrapper() });

    expect(screen.getByTestId('utility-link-0')).toHaveTextContent('Support');
  });

  it('renders contact links correctly', () => {
    mockUseFooter.mockReturnValue({
      footerData: mockFooterData,
      error: null,
    });

    render(<Footer />, { wrapper: createWrapper() });

    expect(screen.getByTestId('contact-link-0')).toHaveTextContent('Contact Us');
  });

  it('renders social media links correctly', () => {
    mockUseFooter.mockReturnValue({
      footerData: mockFooterData,
      error: null,
    });

    render(<Footer />, { wrapper: createWrapper() });

    expect(screen.getByTestId('social-link-0')).toHaveTextContent('Twitter');
  });

  it('returns null when there is an error', () => {
    mockUseFooter.mockReturnValue({
      footerData: null,
      error: new Error('Failed to fetch footer data'),
    });

    const { container } = render(<Footer />, { wrapper: createWrapper() });

    expect(container.firstChild).toBeNull();
  });

  it('returns null when footerData is null', () => {
    mockUseFooter.mockReturnValue({
      footerData: null,
      error: null,
    });

    const { container } = render(<Footer />, { wrapper: createWrapper() });

    expect(container.firstChild).toBeNull();
  });

  it('returns null when redesignFooter is disabled', () => {
    const footerDataWithDisabledRedesign = {
      ...mockFooterData,
      redesignFooter: {
        value: false
      }
    };

    mockUseFooter.mockReturnValue({
      footerData: footerDataWithDisabledRedesign,
      error: null,
    });

    const { container } = render(<Footer />, { wrapper: createWrapper() });

    expect(container.firstChild).toBeNull();
  });

  it('handles empty arrays for links gracefully', () => {
    const footerDataWithEmptyLinks = {
      ...mockFooterData,
      legalLinks: [],
      utilityLinks: [],
      contactLinks: [],
      socialMediaLinks: []
    };

    mockUseFooter.mockReturnValue({
      footerData: footerDataWithEmptyLinks,
      error: null,
    });

    render(<Footer />, { wrapper: createWrapper() });

    expect(screen.getByTestId('redesign-footer')).toBeInTheDocument();
    expect(screen.queryByTestId('legal-link-0')).not.toBeInTheDocument();
    expect(screen.queryByTestId('utility-link-0')).not.toBeInTheDocument();
    expect(screen.queryByTestId('contact-link-0')).not.toBeInTheDocument();
    expect(screen.queryByTestId('social-link-0')).not.toBeInTheDocument();
  });

  it('transforms legal links icon fields correctly', () => {
    mockUseFooter.mockReturnValue({
      footerData: mockFooterData,
      error: null,
    });

    render(<Footer />, { wrapper: createWrapper() });

    // The component should render without errors, indicating successful transformation
    expect(screen.getByTestId('redesign-footer')).toBeInTheDocument();
    expect(screen.getByTestId('legal-link-0')).toBeInTheDocument();
  });
});
