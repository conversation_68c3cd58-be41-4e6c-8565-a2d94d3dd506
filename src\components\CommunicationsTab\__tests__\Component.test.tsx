import { render, screen } from '@testing-library/react';
import { MockedProvider } from '@apollo/client/testing';
import { CommunicationsTab } from '../Component';
import { CommunicationsTab as CommunicationsTabProps } from '../../../models/CommunicationsTab';

// Mock the ServiceNotificationPreferences component
jest.mock('../../ServiceNotificationPreferences', () => {
  return function MockServiceNotificationPreferences(props: any) {
    return (
      <div data-testid={`service-notification-preferences-${props.name}`}>
        <h3>{props.notificationType?.title}</h3>
        <p>{props.infoDescription}</p>
        <button>{props.saveButtonText}</button>
        <button>{props.editLinkText}</button>
        <button>{props.discardButtonText}</button>
        <button>{props.closeLinkText}</button>
      </div>
    );
  };
});

const mockProps: CommunicationsTabProps = {
  communicationPreferences: [
    {
      name: 'email-notifications',
      infoTitle: 'Email Notifications',
      saveButtonText: 'Save Changes',
      editLinkText: 'Edit Preferences',
      productType: {
        name: 'Healthcare Equipment',
        value: 'healthcare-equipment',
        code: 'HE001'
      },
      infoDescription: 'Manage your email notification preferences for equipment updates and service alerts.',
      notificationType: {
        name: 'email',
        title: 'Email Notifications',
        value: 'email-notifications',
        code: 'EMAIL_001'
      },
      discardButtonText: 'Discard Changes',
      groups: [
        {
          name: 'service-alerts',
          categoryDefinition: 'Service and maintenance alerts',
          disableToggle: false,
          productType: {
            name: 'Healthcare Equipment',
            value: 'healthcare-equipment',
            code: 'HE001'
          },
          groupTitle: 'Service Alerts',
          onLabelText: 'On',
          offLabelText: 'Off',
          items: [
            {
              name: 'maintenance-reminders',
              title: 'Maintenance Reminders',
              code: 'MAINT_001',
              richDescription: 'Receive notifications about scheduled maintenance',
              disableSelection: false,
              emailPreferenceDescription: 'Get email alerts for maintenance schedules',
              description: 'Maintenance reminder notifications'
            }
          ]
        }
      ],
      closeLinkText: 'Close'
    },
    {
      name: 'sms-notifications',
      infoTitle: 'SMS Notifications',
      saveButtonText: 'Save Settings',
      editLinkText: 'Edit SMS Preferences',
      productType: {
        name: 'Mobile Alerts',
        value: 'mobile-alerts',
        code: 'MA001'
      },
      infoDescription: 'Configure SMS notifications for urgent equipment alerts.',
      notificationType: {
        name: 'sms',
        title: 'SMS Notifications',
        value: 'sms-notifications',
        code: 'SMS_001'
      },
      discardButtonText: 'Cancel Changes',
      groups: [
        {
          name: 'urgent-alerts',
          categoryDefinition: 'Critical equipment alerts',
          disableToggle: false,
          productType: {
            name: 'Mobile Alerts',
            value: 'mobile-alerts',
            code: 'MA001'
          },
          groupTitle: 'Urgent Alerts',
          onLabelText: 'Enabled',
          offLabelText: 'Disabled',
          items: [
            {
              name: 'critical-failures',
              title: 'Critical Equipment Failures',
              code: 'CRIT_001',
              richDescription: 'Immediate alerts for critical equipment failures',
              disableSelection: false,
              emailPreferenceDescription: 'SMS alerts for critical failures',
              description: 'Critical failure notifications'
            }
          ]
        }
      ],
      closeLinkText: 'Close Panel'
    }
  ]
};

const emptyProps: CommunicationsTabProps = {
  communicationPreferences: []
};

const singlePreferenceProps: CommunicationsTabProps = {
  communicationPreferences: [
    {
      name: 'single-notification',
      infoTitle: 'Single Notification',
      saveButtonText: 'Save',
      editLinkText: 'Edit',
      productType: {
        name: 'Test Product',
        value: 'test-product',
        code: 'TP001'
      },
      infoDescription: 'Single notification preference for testing.',
      notificationType: {
        name: 'test',
        title: 'Test Notifications',
        value: 'test-notifications',
        code: 'TEST_001'
      },
      discardButtonText: 'Discard',
      groups: [],
      closeLinkText: 'Close'
    }
  ]
};

describe('CommunicationsTab Component', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    render(
      <MockedProvider mocks={[]}>
        <CommunicationsTab {...mockProps} />
      </MockedProvider>
    );
    
    expect(screen.getByTestId('service-notification-preferences-email-notifications')).toBeInTheDocument();
    expect(screen.getByTestId('service-notification-preferences-sms-notifications')).toBeInTheDocument();
  });

  it('renders multiple communication preferences correctly', () => {
    render(
      <MockedProvider mocks={[]}>
        <CommunicationsTab {...mockProps} />
      </MockedProvider>
    );

    // Check that both notification types are rendered
    expect(screen.getByText('Email Notifications')).toBeInTheDocument();
    expect(screen.getByText('SMS Notifications')).toBeInTheDocument();
    
    // Check descriptions are rendered
    expect(screen.getByText('Manage your email notification preferences for equipment updates and service alerts.')).toBeInTheDocument();
    expect(screen.getByText('Configure SMS notifications for urgent equipment alerts.')).toBeInTheDocument();
  });

  it('renders correct button texts for each preference', () => {
    render(
      <MockedProvider mocks={[]}>
        <CommunicationsTab {...mockProps} />
      </MockedProvider>
    );

    // Check email notification buttons
    expect(screen.getByText('Save Changes')).toBeInTheDocument();
    expect(screen.getByText('Edit Preferences')).toBeInTheDocument();
    expect(screen.getByText('Discard Changes')).toBeInTheDocument();
    
    // Check SMS notification buttons
    expect(screen.getByText('Save Settings')).toBeInTheDocument();
    expect(screen.getByText('Edit SMS Preferences')).toBeInTheDocument();
    expect(screen.getByText('Cancel Changes')).toBeInTheDocument();
  });

  it('renders empty state when no communication preferences provided', () => {
    const { container } = render(
      <MockedProvider mocks={[]}>
        <CommunicationsTab {...emptyProps} />
      </MockedProvider>
    );

    // Should render empty fragment with no content
    expect(container.firstChild).toBeNull();
  });

  it('renders single communication preference correctly', () => {
    render(
      <MockedProvider mocks={[]}>
        <CommunicationsTab {...singlePreferenceProps} />
      </MockedProvider>
    );

    expect(screen.getByTestId('service-notification-preferences-single-notification')).toBeInTheDocument();
    expect(screen.getByText('Test Notifications')).toBeInTheDocument();
    expect(screen.getByText('Single notification preference for testing.')).toBeInTheDocument();
  });

  it('handles undefined communicationPreferences gracefully', () => {
    const undefinedProps = { communicationPreferences: undefined } as any;

    const { container } = render(
      <MockedProvider mocks={[]}>
        <CommunicationsTab {...undefinedProps} />
      </MockedProvider>
    );

    expect(container.firstChild).toBeNull();
  });

  it('passes correct props to ServiceNotificationPreferences components', () => {
    render(
      <MockedProvider mocks={[]}>
        <CommunicationsTab {...mockProps} />
      </MockedProvider>
    );

    // Verify that the mocked component receives the correct props
    const emailPreference = screen.getByTestId('service-notification-preferences-email-notifications');
    const smsPreference = screen.getByTestId('service-notification-preferences-sms-notifications');

    expect(emailPreference).toBeInTheDocument();
    expect(smsPreference).toBeInTheDocument();
    
    // Check that close buttons have different texts
    expect(screen.getByText('Close')).toBeInTheDocument();
    expect(screen.getByText('Close Panel')).toBeInTheDocument();
  });
});
