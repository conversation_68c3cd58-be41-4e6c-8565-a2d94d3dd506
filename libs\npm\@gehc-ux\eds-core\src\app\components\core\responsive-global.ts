/**
 *  responsive-global.ts
 *
 *  Copyright (c) 2019 by General Electric Company. All rights reserved.
 *
 *  The copyright to the computer software herein is the property of
 *  General Electric Company. The software may be used and/or copied only
 *  with the written permission of General Electric Company or in accordance
 *  with the terms and conditions stipulated in the agreement/contract
 *  under which the software has been supplied.
 *
 */
import breakpoints from '../../styles/common/breakpoints-util.js';
import { EDSComponent } from './eds-component';

declare global {
  interface Window {
    GEHC: any;
  }
}

/**
 * static class allowing to track responsiveness, in particular screen size
 */
export class GlobalResponsive {

  static initGlobal() {

    window.GEHC = window.GEHC || {};
    window.GEHC.EDS = window.GEHC.EDS || {};
    window.GEHC.EDS.responsive =  window.GEHC.EDS.responsive || {};
    window.GEHC.EDS.responsive.screenSize = 'none';
    window.GEHC.EDS.responsive.debounceTiming = window.GEHC.EDS.responsive.debounceTiming || 500;

    window.onresize = EDSComponent.debounce(function() {
      GlobalResponsive.onResize();
    }, window.GEHC.EDS.responsive.debounceTiming);

    GlobalResponsive.onResize();
  }

  /**
   * Listens to the window and sets the current window size
   */
  static onResize() {
    window.requestAnimationFrame(() => {

      const previousScreen = window.GEHC.EDS.responsive.screenSize;
      let fire = false;

      if (window.innerWidth < parseInt(breakpoints.edsBreakpointSmall, 10)) {
        if (window.GEHC.EDS.responsive.screenSize !== 'mobile') {
          window.GEHC.EDS.responsive.screenSize = 'mobile';
          fire = true;
        }
       } else if (window.innerWidth < parseInt(breakpoints.edsBreakpointMedium, 10)) {
        if (window.GEHC.EDS.responsive.screenSize !== 'tablet') {
          window.GEHC.EDS.responsive.screenSize = 'tablet';
          fire = true;
        }
      } else if (window.innerWidth >= parseInt(breakpoints.edsBreakpointMedium, 10)) {
        if (window.GEHC.EDS.responsive.screenSize !== 'desktop') {
          window.GEHC.EDS.responsive.screenSize = 'desktop';
          fire = true;
        }
      }

      if (fire) {
        GlobalResponsive.fire({ previous: previousScreen, current: window.GEHC.EDS.responsive.screenSize})
      }

    });
  }

  /**
   * fires a `eds-screen-size-changed` event with previous and current value
   * @param detail 
   */
  static fire(detail) {
    let event;

    // On IE11, `CustomEvent` is not a constructor.
    if (typeof CustomEvent !== 'function') {
      event = document.createEvent('CustomEvent');
      event.initCustomEvent(name, false, false, detail);
      return event;
    } else {
      event = new CustomEvent('eds-screen-size-changed', {
        bubbles: true,
        cancelable: false,
        composed: true,
        detail: detail
      });
    }

    window.dispatchEvent(event);
  }
}
