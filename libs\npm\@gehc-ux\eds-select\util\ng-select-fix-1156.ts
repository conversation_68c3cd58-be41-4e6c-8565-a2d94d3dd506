/**
 * This class represents an isolated set of functions to allow users to 
 * edit an existing selected value of the eds-select component (which
 * uses ng-select underneath) when using the search function (allowing
 * user input by keyboard). ng-select has unresolved issue 1156 about this.  
 * The hack workaround here is a derived form from that which was proposed 
 * by sillybutt0 here
 * https://github.com/ng-select/ng-select/issues/1088#issuecomment-476087474
 * 
 * ng-select issue which has a number of features in it 
 * https://github.com/ng-select/ng-select/issues/1156
 */
export class NgSelectFix1156 {
  
  /**
   * instance of eds-select component
   */
  protected edsSelect: any;

  /**
   * the input field buried in the ng-select component that is used for user
   * input when using search feature.
   */
  private selectInput: HTMLInputElement;
  
  /** 
   * 
   * the "this" of consumer. used for parent object reference to selected value
   * for the select component
   */
  private context: any; 

  /** 
   * 
   * the name of the property on the select component that is used to 
   * store the selected value of eds-select.  This is used for runtime binding
   * as it is not found at constructor time.
   */
  private selectedValueProperty: string; 
  
  /**
   * constructor 
   *
   * @param options config option. Must have
   *   edsSelect -- a reference to consumer's eds-select component instance
   *                ususally derived from ViewChild reference
   *   context -- the `this` of the consuming class.
   *   selectedValueProperty: string -- the string name of the property on 
   *            the consuming eds-selcet component that stores the `selectedValue`
   *            on the consuming class.  This is used for runtime binding to
   *            the selectedValue
   */
  constructor(options:any) {
    this.edsSelect = options.edsSelect;
    this.context = options.context;
    this.selectedValueProperty = options.selectedValueProperty;
    // this is the underlying html input field found inside the ng-select component.
    this.selectInput = this.edsSelect.select.element.querySelector("input[role='combobox']");
  }

  /** hide the displayed value of eds-select
   * this span element is the display value user sees on screen (shows the
   * selected value in a span element (not the input el that is used for
   * user input))
   */
  hideDisplayLabel() {
    const displayLabel = this.edsSelect.select.element.querySelector(".select__value-label");
    if (displayLabel) {
      displayLabel.setAttribute('style', 'opacity: 0');
    }
  }

  /** show the displayed value of eds-select
   * this span element is the display value user sees on screen (shows the
   * selected value in a span element (not the input el that is used for
   * user input))
   */
  showDisplayLabel() {
    const displayLabel = this.edsSelect.select.element.querySelector(".select__value-label");
    if (displayLabel) {
      displayLabel.setAttribute('style', 'opacity: 1');
    }
  }

  /**
   * This forces the underlying input field of ng-select to lose focus.
   * This should be called after the options list is closed (select.close())
   * This is needed to allow the user to be able to re-click on the element
   * and have it take focus again. Importantly, it triggers ng-select's 
   * `onInputBlur()` which sets its `focused` state to false, allowing 
   * further interaction.
   */
  blurInputField() {
    this.selectInput.blur();
  }
  
  /**
   * this sets the underlying input field to the selected value (if set)
   * should be called with the time-picker (ng-select) takes focus.
   */
  setInputValue() {
    if (!!this.selectInput && !!this.context[this.selectedValueProperty]) {
      this.selectInput.value = this.context[this.selectedValueProperty].label; 
      // clear the text display label to avoid double rendering (bold effect)
      this.hideDisplayLabel();
    }
  }

  /**
   *  clear the input field value. This should be called after a value
   * has been selected from the available valid values in the select list
   */
  clearInputValue() {
    if (!!this.selectInput && !!this.context[this.selectedValueProperty]) {
      this.selectInput.value = '';
      // we cleared the display label earlier, so need to put it back
      this.showDisplayLabel(); 
    }
  }
}
