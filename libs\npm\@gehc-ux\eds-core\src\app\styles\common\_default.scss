// Font families
$eds-base-font-family: var(--font-family-base);
$eds-base-font-family-bold: $eds-base-font-family;
$eds-base-mono-font-family: var(--font-family-monospace);

// TODO: Replace with official fonts
$eds-arabic-font-family: "noto-kufi", Aria<PERSON>;
$eds-chinese-font-family: "noto-sans", Aria<PERSON>;
$eds-cyrillic-font-family: Arial;
$eds-japanese-font-family: Arial;
$eds-korean-font-family: Arial;

// Font sizes
$eds-base-font-size: 10px;
$eds-tiny-font-size: 7px;
$eds-small-font-size: 12px;
$eds-small-medium-font-size: 13px;
$eds-medium-font-size: 14px;
$eds-large-font-size: 16px;
$eds-large-medium-font-size: 18px;
$eds-xlarge-font-size: 20px;

@function calculateRem($size) {
  $remSize: calc($size / $eds-base-font-size);
  $remUnit: 1rem;
  @return $remSize * $remUnit;
}

// Spacing
$eds-base-spacing-unit: calculateRem($eds-base-font-size); // 10px
$eds-base-spacing-unit--xtiny: calculateRem(round($eds-base-font-size * .4)); // 4px
$eds-base-spacing-unit--tiny: calculateRem(round($eds-base-font-size * .8)); // 8px
$eds-base-spacing-unit--xsmall: calculateRem(round($eds-base-font-size * 1.2)); // 12px
$eds-base-spacing-unit--small: calculateRem(round($eds-base-font-size * 1.6)); // 16px
$eds-base-spacing-unit--small-medium: calculateRem(round($eds-base-font-size * 1.6)); // 16px
$eds-base-spacing-unit--medium: calculateRem(round($eds-base-font-size * 2)); // 20px
$eds-base-spacing-unit--large: calculateRem($eds-base-font-size * 3); // 30px
$eds-base-spacing-unit--xlarge: calculateRem($eds-base-font-size * 4); // 40px
$eds-base-spacing-unit--huge: calculateRem(round($eds-base-font-size * 8)); // 80px

// Widths
$eds-width-full: 100%;
$eds-width-half: 50%;
$eds-width-third: 33.333333%;
$eds-width-quarter: 25%;
$eds-width-fifth: 20%;
$eds-width-sixth: 16.666667%;
$eds-width-seventh: 14.28571429%;
$eds-width-eighth: 12.5%;

// borders
$eds-hr-border-width: 0;
$eds-hr-border-bottom-width: 1px;
