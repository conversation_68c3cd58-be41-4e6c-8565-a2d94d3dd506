import { render, screen } from '@testing-library/react';
import { NotificationsGroupedList } from '../Component';
import { NotificationsGroupedListProps } from '../models';
import { ServiceNotification, NotificationObjectType } from '../../NotificationCardV2/models';
import { GroupingRuleType } from '../../../../../models/Notifications';

// Mock dependencies
jest.mock('cx-dle-component-library', () => ({
  LoadableSection: ({ loading, children }: { loading: boolean; children: () => any }) => (
    <div data-testid="loadable-section" data-loading={loading}>
      {children()}
    </div>
  ),
  RichText: ({ text }: { text: string }) => (
    <div data-testid="rich-text">{text}</div>
  ),
}));

jest.mock('../NotificationsGroup', () => {
  const MockNotificationsGroup = ({
    group,
    buttonText,
    warrantyExpiredText,
    onWatchMessageText,
    contractExpiredText,
    overduePMText,
    onHoldMessageText,
    selectRequestAsset,
    dateFormat
  }: any) => (
    <div
      data-testid="notifications-group"
      data-group-title={group.title}
      data-group-items-count={group.items.length}
      data-button-text={buttonText}
      data-warranty-expired-text={warrantyExpiredText}
      data-on-watch-message-text={onWatchMessageText}
      data-contract-expired-text={contractExpiredText}
      data-overdue-pm-text={overduePMText}
      data-on-hold-message-text={onHoldMessageText}
      data-date-format={dateFormat}
    >
      <span>Group: {group.title}</span>
      <span>Items: {group.items.length}</span>
    </div>
  );

  return MockNotificationsGroup;
});

jest.mock('cx-dle-common-lib', () => ({
  useLocaleMoment: jest.fn(() => ({
    moment: jest.fn(() => ({
      subtract: jest.fn(() => ({
        utc: jest.fn(() => ({
          isBetween: jest.fn(() => true),
        })),
      })),
      utc: jest.fn(() => ({
        isBetween: jest.fn(() => true),
      })),
    })),
  })),
}));

jest.mock('../../../../../context', () => ({
  useLanguage: jest.fn(() => ({
    language: 'en-us',
  })),
}));

// Mock the grouping function
jest.mock('../grouping', () => ({
  groupItems: jest.fn(),
}));

import { groupItems } from '../grouping';

describe('NotificationsGroupedList Component', () => {
  const mockSelectRequestAsset = jest.fn();
  const mockShowNoMessage = jest.fn();
  const mockGroupItems = groupItems as jest.MockedFunction<typeof groupItems>;

  const baseServiceNotification: ServiceNotification = {
    assetRowId: 'asset-123',
    read: false,
    subject: 'Test Notification 1',
    orderTimestamp: '2023-01-01T10:00:00Z',
    notificationId: 'notification-123',
    user: {
      id: 'user-123',
      firstname: 'John',
      lastname: 'Doe',
      email: '<EMAIL>',
      locale: 'en-us',
    },
    eventData: {
      eventId: 'event-123',
      eventTimestamp: '2023-01-01T08:00:00Z',
      physicalLocationName: 'Main Hospital',
      physicalLocationCity: 'New York',
      physicalLocationRegion: 'NY',
      rowId: 'event-row-123',
    },
    creationTimestamp: '2023-01-01T10:00:00Z',
    objectRowId: 'object-123',
    objectType: NotificationObjectType.ServiceRequest,
    modality: {
      id: 'CT',
      abbreviation: 'CT',
      description: 'Computed Tomography',
      preferred: true,
      isActive: true,
      modalityDisplayName: 'CT Scanner',
    },
    productDescription: 'CT Scanner Model XYZ',
    equipmentId: 'EQ-12345',
    engineerTypeCode: 'ENG01',
    serviceStateMessageCode: 'MSG01',
    serviceStateGeneratedMessage: 'Service in progress',
    sfdcNotificationFlag: {
      code: 'FLAG01',
      title: 'Service Request',
      icon: {
        iconName: 'service-icon',
        iconPrefix: 'ico',
        iconBackground: 'primary',
      },
    },
    requester: {
      fullName: 'Jane Smith',
    },
    contractExpirationDate: '2024-12-31',
    warrantyExpirationDate: '2024-06-30',
    endOfServiceLife: '2025-12-31',
  };

  const mockGroupingRules: GroupingRuleType[] = [
    {
      id: 'today',
      name: 'Today',
      dateFormat: 'MM/DD/YYYY',
      groupTitleTemplate: 'Today, {dateFormat}',
      startOfTheNThDay: '0',
      endOfTheNThDay: '0',
    },
    {
      id: 'yesterday',
      name: 'Yesterday',
      dateFormat: 'MM/DD/YYYY',
      groupTitleTemplate: 'Yesterday, {dateFormat}',
      startOfTheNThDay: '1',
      endOfTheNThDay: '1',
    },
  ];

  const baseProps: NotificationsGroupedListProps = {
    items: [baseServiceNotification],
    buttonText: 'View Details',
    groupingRules: mockGroupingRules,
    warrantyExpiredText: 'Warranty Expired',
    onWatchMessageText: 'On Watch',
    contractExpiredText: 'Contract Expired',
    overduePMText: 'Overdue PM',
    onHoldMessageText: 'On Hold',
    dateFormat: 'MM/DD/YYYY',
    selectRequestAsset: mockSelectRequestAsset,
    noNotificationMessage: 'No notifications available',
    showNoMessage: mockShowNoMessage,
    loading: false,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockGroupItems.mockReturnValue({
      today: {
        title: 'Today',
        items: [baseServiceNotification],
      },
    });
  });

  describe('Basic Rendering', () => {
    it('renders without crashing', () => {
      render(<NotificationsGroupedList {...baseProps} />);
      expect(screen.getByTestId('notifications-group')).toBeInTheDocument();
    });

    it('calls groupItems with correct parameters', () => {
      render(<NotificationsGroupedList {...baseProps} />);
      expect(mockGroupItems).toHaveBeenCalledWith(
        [baseServiceNotification],
        mockGroupingRules,
        expect.any(Object)
      );
    });

    it('renders notification groups when groups exist', () => {
      render(<NotificationsGroupedList {...baseProps} />);
      
      const notificationGroup = screen.getByTestId('notifications-group');
      expect(notificationGroup).toBeInTheDocument();
      expect(notificationGroup).toHaveAttribute('data-group-title', 'Today');
      expect(notificationGroup).toHaveAttribute('data-group-items-count', '1');
    });

    it('passes correct props to NotificationsGroup components', () => {
      render(<NotificationsGroupedList {...baseProps} />);
      
      const notificationGroup = screen.getByTestId('notifications-group');
      expect(notificationGroup).toHaveAttribute('data-button-text', 'View Details');
      expect(notificationGroup).toHaveAttribute('data-warranty-expired-text', 'Warranty Expired');
      expect(notificationGroup).toHaveAttribute('data-on-watch-message-text', 'On Watch');
      expect(notificationGroup).toHaveAttribute('data-contract-expired-text', 'Contract Expired');
      expect(notificationGroup).toHaveAttribute('data-overdue-pm-text', 'Overdue PM');
      expect(notificationGroup).toHaveAttribute('data-on-hold-message-text', 'On Hold');
      expect(notificationGroup).toHaveAttribute('data-date-format', 'MM/DD/YYYY');
    });
  });

  describe('Multiple Groups Rendering', () => {
    it('renders multiple notification groups', () => {
      const secondNotification = {
        ...baseServiceNotification,
        notificationId: 'notification-456',
        subject: 'Test Notification 2',
      };

      mockGroupItems.mockReturnValue({
        today: {
          title: 'Today',
          items: [baseServiceNotification],
        },
        yesterday: {
          title: 'Yesterday',
          items: [secondNotification],
        },
      });

      render(<NotificationsGroupedList {...baseProps} />);
      
      const notificationGroups = screen.getAllByTestId('notifications-group');
      expect(notificationGroups).toHaveLength(2);
      
      expect(notificationGroups[0]).toHaveAttribute('data-group-title', 'Today');
      expect(notificationGroups[1]).toHaveAttribute('data-group-title', 'Yesterday');
    });

    it('renders groups with correct item counts', () => {
      const secondNotification = {
        ...baseServiceNotification,
        notificationId: 'notification-456',
      };

      mockGroupItems.mockReturnValue({
        today: {
          title: 'Today',
          items: [baseServiceNotification, secondNotification],
        },
      });

      render(<NotificationsGroupedList {...baseProps} />);
      
      const notificationGroup = screen.getByTestId('notifications-group');
      expect(notificationGroup).toHaveAttribute('data-group-items-count', '2');
    });
  });

  describe('Loading State', () => {
    it('renders LoadableSection when loading is true and items array is empty', () => {
      const loadingProps = {
        ...baseProps,
        loading: true,
        items: [],
      };

      render(<NotificationsGroupedList {...loadingProps} />);
      
      const loadableSection = screen.getByTestId('loadable-section');
      expect(loadableSection).toBeInTheDocument();
      expect(loadableSection).toHaveAttribute('data-loading', 'true');
    });

    it('does not render LoadableSection when loading is true but items exist', () => {
      const loadingProps = {
        ...baseProps,
        loading: true,
        items: [baseServiceNotification],
      };

      render(<NotificationsGroupedList {...loadingProps} />);
      
      expect(screen.queryByTestId('loadable-section')).not.toBeInTheDocument();
      expect(screen.getByTestId('notifications-group')).toBeInTheDocument();
    });

    it('does not render LoadableSection when loading is false', () => {
      render(<NotificationsGroupedList {...baseProps} />);
      
      expect(screen.queryByTestId('loadable-section')).not.toBeInTheDocument();
    });
  });

  describe('No Data State', () => {
    it('renders no data message when no groups exist', () => {
      mockGroupItems.mockReturnValue({});

      render(<NotificationsGroupedList {...baseProps} />);

      expect(screen.getByTestId('rich-text')).toBeInTheDocument();
      expect(screen.getByText('No notifications available')).toBeInTheDocument();
      expect(screen.getByText('No notifications available').closest('div')).toHaveClass('ge-notifications-list-no-data');
    });

    it('calls showNoMessage with true when no groups exist', () => {
      mockGroupItems.mockReturnValue({});

      render(<NotificationsGroupedList {...baseProps} />);

      expect(mockShowNoMessage).toHaveBeenCalledWith(true);
    });

    it('does not render no data message when groups exist', () => {
      render(<NotificationsGroupedList {...baseProps} />);

      expect(screen.queryByTestId('rich-text')).not.toBeInTheDocument();
      expect(screen.queryByText('No notifications available')).not.toBeInTheDocument();
    });

    it('does not call showNoMessage when groups exist', () => {
      render(<NotificationsGroupedList {...baseProps} />);

      expect(mockShowNoMessage).not.toHaveBeenCalled();
    });
  });

  describe('Edge Cases', () => {
    it('handles empty items array', () => {
      const emptyProps = {
        ...baseProps,
        items: [],
      };

      mockGroupItems.mockReturnValue({});

      render(<NotificationsGroupedList {...emptyProps} />);

      expect(mockGroupItems).toHaveBeenCalledWith([], mockGroupingRules, expect.any(Object));
      expect(screen.getByTestId('rich-text')).toBeInTheDocument();
    });

    it('handles empty grouping rules', () => {
      const emptyRulesProps = {
        ...baseProps,
        groupingRules: [],
      };

      mockGroupItems.mockReturnValue({});

      render(<NotificationsGroupedList {...emptyRulesProps} />);

      expect(mockGroupItems).toHaveBeenCalledWith([baseServiceNotification], [], expect.any(Object));
    });

    it('handles single item in single group', () => {
      render(<NotificationsGroupedList {...baseProps} />);

      const notificationGroups = screen.getAllByTestId('notifications-group');
      expect(notificationGroups).toHaveLength(1);
      expect(notificationGroups[0]).toHaveAttribute('data-group-items-count', '1');
    });

    it('handles custom no notification message', () => {
      const customMessageProps = {
        ...baseProps,
        noNotificationMessage: 'Custom no data message',
      };

      mockGroupItems.mockReturnValue({});

      render(<NotificationsGroupedList {...customMessageProps} />);

      expect(screen.getByText('Custom no data message')).toBeInTheDocument();
    });
  });

  describe('Language Context Integration', () => {
    it('uses language from context for locale moment', () => {
      const { useLanguage } = require('../../../../../context');

      render(<NotificationsGroupedList {...baseProps} />);

      expect(useLanguage).toHaveBeenCalled();
    });

    it('handles different language settings', () => {
      const { useLanguage } = require('../../../../../context');
      useLanguage.mockReturnValue({ language: 'fr-fr' });

      render(<NotificationsGroupedList {...baseProps} />);

      expect(mockGroupItems).toHaveBeenCalledWith(
        [baseServiceNotification],
        mockGroupingRules,
        expect.any(Object)
      );
    });
  });

  describe('Props Validation', () => {
    it('passes all required props to NotificationsGroup', () => {
      render(<NotificationsGroupedList {...baseProps} />);

      const notificationGroup = screen.getByTestId('notifications-group');

      // Verify all text props are passed correctly
      expect(notificationGroup).toHaveAttribute('data-button-text', baseProps.buttonText);
      expect(notificationGroup).toHaveAttribute('data-warranty-expired-text', baseProps.warrantyExpiredText);
      expect(notificationGroup).toHaveAttribute('data-on-watch-message-text', baseProps.onWatchMessageText);
      expect(notificationGroup).toHaveAttribute('data-contract-expired-text', baseProps.contractExpiredText);
      expect(notificationGroup).toHaveAttribute('data-overdue-pm-text', baseProps.overduePMText);
      expect(notificationGroup).toHaveAttribute('data-on-hold-message-text', baseProps.onHoldMessageText);
      expect(notificationGroup).toHaveAttribute('data-date-format', baseProps.dateFormat);
    });

    it('handles missing optional props gracefully', () => {
      const minimalProps = {
        items: [baseServiceNotification],
        buttonText: 'View Details',
        groupingRules: mockGroupingRules,
        warrantyExpiredText: 'Warranty Expired',
        onWatchMessageText: 'On Watch',
        contractExpiredText: 'Contract Expired',
        overduePMText: 'Overdue PM',
        onHoldMessageText: 'On Hold',
        dateFormat: 'MM/DD/YYYY',
        selectRequestAsset: mockSelectRequestAsset,
        noNotificationMessage: 'No notifications available',
        showNoMessage: mockShowNoMessage,
        loading: false,
      };

      render(<NotificationsGroupedList {...minimalProps} />);

      expect(screen.getByTestId('notifications-group')).toBeInTheDocument();
    });
  });

  describe('Component Behavior', () => {
    it('renders groups in the order returned by groupItems', () => {
      mockGroupItems.mockReturnValue({
        yesterday: {
          title: 'Yesterday',
          items: [baseServiceNotification],
        },
        today: {
          title: 'Today',
          items: [baseServiceNotification],
        },
      });

      render(<NotificationsGroupedList {...baseProps} />);

      const notificationGroups = screen.getAllByTestId('notifications-group');
      expect(notificationGroups).toHaveLength(2);

      // Order should match the object key order
      expect(notificationGroups[0]).toHaveAttribute('data-group-title', 'Yesterday');
      expect(notificationGroups[1]).toHaveAttribute('data-group-title', 'Today');
    });

    it('re-renders when items change', () => {
      const { rerender } = render(<NotificationsGroupedList {...baseProps} />);

      expect(mockGroupItems).toHaveBeenCalledTimes(1);

      const newNotification = {
        ...baseServiceNotification,
        notificationId: 'new-notification',
      };

      const updatedProps = {
        ...baseProps,
        items: [baseServiceNotification, newNotification],
      };

      rerender(<NotificationsGroupedList {...updatedProps} />);

      expect(mockGroupItems).toHaveBeenCalledTimes(2);
      expect(mockGroupItems).toHaveBeenLastCalledWith(
        [baseServiceNotification, newNotification],
        mockGroupingRules,
        expect.any(Object)
      );
    });

    it('re-renders when grouping rules change', () => {
      const { rerender } = render(<NotificationsGroupedList {...baseProps} />);

      const newGroupingRules = [
        ...mockGroupingRules,
        {
          id: 'last-week',
          name: 'Last Week',
          dateFormat: 'MM/DD/YYYY',
          groupTitleTemplate: 'Last Week, {dateFormat}',
          startOfTheNThDay: '7',
          endOfTheNThDay: '2',
        },
      ];

      const updatedProps = {
        ...baseProps,
        groupingRules: newGroupingRules,
      };

      rerender(<NotificationsGroupedList {...updatedProps} />);

      expect(mockGroupItems).toHaveBeenLastCalledWith(
        [baseServiceNotification],
        newGroupingRules,
        expect.any(Object)
      );
    });
  });
});
