/**
*  _slide-animation.scss
*
*
*
*/

@import '../common/variables';

/**
* SLIDE ANIMATION (4 Types, up, down, left, right, each with an easing modifier)
* SLIDE animation uses a map to generate slide levels and is configured to use easings to indicate entering/exiting.
* The slide animation will animate opacity and transform properties. Does not adjust display property. 
**/

/* 
* SLIDE X
* Slides left or right
*/

    /* 
    * SLIDE IN - Uses the enter easing
    */

    @include block('#{$animate}') {
      @include element('slide-in') {
        @each $level, $slide-x in $slide-x-map {
            @include modifier('#{$level}') {
              animation-name: slide-in-x-#{$level}; 
              animation-fill-mode: forwards;
              animation-timing-function: $exit-curve;
            }
        }
      }
    }

    @each $level, $slide-x in $slide-x-map {
      @keyframes slide-in-x-#{$level} {
            0% {
              opacity: 0;
              transform: translateX(0);
            }
            100% {
              opacity: 1;
              transform: translateX(#{$slide-x});
            }
        }
    }

    /* 
    * SLIDE OUT- Uses the exit easing
    */

    @include block('#{$animate}') {
      @include element('slide-out') {
        @each $level, $slide-x in $slide-x-map {
            @include modifier('#{$level}') {
              animation-name: slide-out-x-#{$level}; 
              animation-fill-mode: forwards;
              animation-timing-function: $exit-curve;
            }
        }
      }
    }

    @each $level, $slide-x in $slide-x-map {
      @keyframes slide-out-x-#{$level} {
            0% {
              opacity: 1;
              transform: translateX(0);
            }
            100% {
              opacity: 0;
              transform: translateX(#{$slide-x});
            }
        }
    }
      


