/**
SPEEDS: There are 6 speeds to be assigned with either a transition or an animation.
**/

/** Speeds are generated as classes. Example:'animate__speed-300ms' They can be used for either animation or duration **/

@import './variables';

  @include block('#{$animate}') {
    @each $el, $speed in $speed-map {
        @include element('speed-#{$speed}') {
          animation-duration: #{$speed}; 
          transition-duration: #{$speed};
        }
    }
  }