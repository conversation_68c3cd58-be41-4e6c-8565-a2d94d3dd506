# See http://help.github.com/ignore-files/ for more about ignoring files.

# compiled output
/dist
/tmp
/out-tsc
htmlReports/**
/htmlReports

# dependencies
/node_modules

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# misc
/.sass-cache
/connect.lock
/coverage
/libpeerconnection.log
npm-debug.log
yarn-error.log
testem.log
/typings
*.css
*.css.map
.scannerwork
.scannerwork/*

# System Files
.DS_Store
Thumbs.db
package-lock.json
src/app/themes/lights-on.scss
src/test-data/color-map.json

#generated doc
documentation
docs
/**/*_api.json
.npmrc

# generated dynamically by build
components-meta.js
angular.json
tsconfig.json
build
