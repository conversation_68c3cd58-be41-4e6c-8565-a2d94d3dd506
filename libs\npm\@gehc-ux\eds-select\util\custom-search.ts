/**
 *  custom-search.ts
 *
 *  Copyright (c) 2019 by General Electric Company. All rights reserved.
 *
 *  The copyright to the computer software herein is the property of
 *  General Electric Company. The software may be used and/or copied only
 *  with the written permission of General Electric Company or in accordance
 *  with the terms and conditions stipulated in the agreement/contract
 *  under which the software has been supplied.
 *
 */

export class CustomSearch {

  constructor() {}

  /**
   * given a label that has multiple words (separated by whitespace)
   * this search function will only return items whose words start with
   * the search term
   */
  public static getSearchForWordsStartingWith() {

    // this map, keyed by a normalized item.label, keeps track, word by word
    // if the search term is matching the complete label.  after each word
    // is checked, the value of the map is set to true/false.
    const labelMatchMap = {};

    // as per ng-select custom search, first parameter is the search term,
    // second parameter is the item in the items array.
    // this function returns a boolean indicating whether the item as a word
    // that starts with the term.
    return function(term, item):boolean {
      if (!item.label) {
        return false;
      }

      // lowercase and collapse any multi whitespace to single space
      const termNormalized = term.toLowerCase().replace(/\s+/, ' ');
      const labelNormalized = item.label.toLowerCase().replace(/\s+/, ' ');
      
      // for multi word labels and words, break them into arrays for iteration
      const labelWords = labelNormalized.split(' ');
      const termWords = termNormalized.split(' ');
  
      if (termWords.length > 1) {
        for (let t = 0; t < termWords.length; t++) {
          const currentTermWord = termWords[t];
          const nextTermWord = t < termWords.length-1 ? termWords[t + 1] : undefined;
          if (currentTermWord.length === 0 && 'undefined' === typeof nextTermWord) {
            // we are at the end of termWords with a trailing space
            // if the label does not contain the trailing space, remove it
            if (!labelNormalized.startsWith(termNormalized)) {
              labelMatchMap[labelNormalized] = false;
            }
            continue;
          }
          const currentMatchingLabelIndex = labelWords.findIndex(
            labelWord => labelWord === currentTermWord
          );
          if ('undefined' === typeof nextTermWord) {
            // no more words left in term words
            break;
          }
          if (nextTermWord.length === 0) {
            // term had trailing space; continue
            continue;
          }
          if (currentMatchingLabelIndex === -1) {
            labelMatchMap[labelNormalized] = false;
            break;
          }
          const nextLabelWord = labelWords[currentMatchingLabelIndex + 1];
          if (nextLabelWord) {
            if (nextLabelWord.startsWith(nextTermWord)) {
              labelMatchMap[labelNormalized] = true;
              continue;
            } else {
              labelMatchMap[labelNormalized] = false;
              break;
            }
          } else {
            labelMatchMap[labelNormalized] = false;
            break;
          }
        }
        return labelMatchMap[labelNormalized];
      } else {
        // simplest case; one-word search term. see if any word in label 
        // starts with this term.
        const someMatch = labelWords.some(label => label.startsWith(termWords[0]));
        if (someMatch) {
          labelMatchMap[labelNormalized] = true;
        }
        return someMatch;
      }
    }
  }

  public static ignoreSearch() {

    // returns true and ignores the string that is typed in the input
    return function():boolean {
      return true;
    }
  }
}
