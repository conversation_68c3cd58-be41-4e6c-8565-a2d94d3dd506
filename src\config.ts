// /**
//  * Config for Microapp. This will hold the all configuration that <PERSON><PERSON><PERSON> needs to run the app.
//  *
//  * As a good rule of thumb, app related and api related configurations are kept separately.
//  */
// /**
//  * Microapp specific configuration. Any microapp octopus variable should start with #{Config.App.*}
//  * For example: #{Config.APP.Hostname.US}, #{Config.App.CDN_BASE_URL}
//  */
// const appConfig = {
//   locales: {
//     us: {
//       localeCode: 'en-us', // Keep lowercased version of locale for consistency
//       hostname: '#{Config.App.US.Hostname}', // Should be only hostname part of the URL without port
//       momentLocale: '', // Empty string will not trigger locale update in the application. Will fallback to en locale only.
//       apiGatewayRegion: 'US',
//     },
//     br: {
//       localeCode: 'pt-br', // Keep lowercased version of locale for consistency
//       hostname: '#{Config.App.BR.Hostname}', // Should be only hostname part of the URL without port
//       momentLocale: 'pt-br', // Empty string will not trigger locale update in the application. Will fallback to en locale only.
//       apiGatewayRegion: 'LA',
//     },
//     enza: {
//       localeCode: 'en-africa', // Keep lowercased version of locale for consistency
//       hostname: '#{Config.App.US.Hostname}', // Should be only hostname part of the URL without port
//       momentLocale: '', // Empty string will not trigger locale update in the application. Will fallback to en locale only.
//       apiGatewayRegion: 'EU',
//     },
//     frza: {
//       localeCode: 'fr-africa', // Keep lowercased version of locale for consistency
//       hostname: '#{Config.App.US.Hostname}', // Should be only hostname part of the URL without port
//       momentLocale: 'fr', // Empty string will not trigger locale update in the application. Will fallback to en locale only.
//       apiGatewayRegion: 'EU',
//     },
//     frus: {
//       localeCode: 'fr-fr', // Keep lowercased version of locale for consistency
//       hostname: '#{Config.App.US.Hostname}', // Should be only hostname part of the URL without port
//       momentLocale: 'fr', // Empty string will not trigger locale update in the application. Will fallback to en locale only.
//       apiGatewayRegion: 'EU',
//     },
//     latam: {
//       localeCode: 'es-pa', // Keep lowercased version of locale for consistency
//       hostname: '#{Config.App.LATAM.Hostname}', // Should be only hostname part of the URL without port
//       momentLocale: 'es', // Empty string will not trigger locale update in the application. Will fallback to en locale only.
//       apiGatewayRegion: 'LA',
//     },
//     in: {
//       localeCode: 'en-in', // Keep lowercased version of locale for consistency
//       hostname: '#{Config.App.IN.Hostname}', // Should be only hostname part of the URL without port
//       momentLocale: '',
//       apiGatewayRegion: 'EU',
//     },
//     enca: {
//       localeCode: 'en-ca', // Keep lowercased version of locale for consistency
//       hostname: '#{Config.App.CA.Hostname}', // Should be only hostname part of the URL without port
//       apiGatewayRegion: 'US',
//     },
//     frca: {
//       localeCode: 'fr-ca', // Keep lowercased version of locale for consistency
//       hostname: '#{Config.App.CA.Hostname}', // Should be only hostname part of the URL without port
//       momentLocale: 'fr',
//       apiGatewayRegion: 'US',
//     },
//     // EIPP Specific
//     se: {
//       localeCode: 'sv-se', // Keep lowercased version of locale for consistency
//       hostname: '#{Config.App.SE.Hostname}', // Should be only hostname part of the URL without port
//       momentLocale: '',
//       apiGatewayRegion: 'EU',
//     },
//     de: {
//       localeCode: 'de-de', // Keep lowercased version of locale for consistency
//       hostname: '#{Config.App.DE.Hostname}', // Should be only hostname part of the URL without port
//       momentLocale: '',
//       apiGatewayRegion: 'EU',
//     },
//     au: {
//       localeCode: 'en-au', // Keep lowercased version of locale for consistency
//       hostname: '#{Config.App.AU.Hostname}', // Should be only hostname part of the URL without port
//       momentLocale: '',
//       apiGatewayRegion: 'EU',
//     },
//     uk: {
//       localeCode: 'en-gb', // Keep lowercased version of locale for consistency
//       hostname: '#{Config.App.UK.Hostname}', // Should be only hostname part of the URL without port
//       momentLocale: '',
//       apiGatewayRegion: 'EU',
//     },
//     sg: {
//       localeCode: 'en-sg', // Keep lowercased version of locale for consistency
//       hostname: '#{Config.App.US.Hostname}', // Should be only hostname part of the URL without port
//       momentLocale: '',
//       apiGatewayRegion: 'EU',
//     },
//     middleEast: {
//       localeCode: 'middle-east', // Keep lowercased version of locale for consistency
//       hostname: '#{Config.App.US.Hostname}', // Should be only hostname part of the URL without port
//       momentLocale: '',
//       apiGatewayRegion: 'EU',
//     },
//     romania: {
//       localeCode: 'ro-ro', // Keep lowercased version of locale for consistency
//       hostname: '#{Config.App.RO.Hostname}', // Should be only hostname part of the URL without port
//       momentLocale: '',
//       apiGatewayRegion: 'EU',
//     },
//     es: {
//       localeCode: 'es-es', // Keep lowercased version of locale for consistency
//       hostname: '#{Config.App.ES.Hostname}', // Should be only hostname part of the URL without port
//       momentLocale: '',
//       apiGatewayRegion: 'EU',
//     },
//     pl: {
//       localeCode: 'po-pl', // Keep lowercased version of locale for consistency
//       hostname: '#{Config.App.PO.Hostname}', // Should be only hostname part of the URL without port
//       momentLocale: '',
//       apiGatewayRegion: 'EU',
//     },
//     dk: {
//       localeCode: 'da-dk', // Keep lowercased version of locale for consistency
//       hostname: '#{Config.App.DK.Hostname}', // Should be only hostname part of the URL without port
//       momentLocale: '',
//       apiGatewayRegion: 'EU',
//     },
//     fi: {
//       localeCode: 'fi-fi', // Keep lowercased version of locale for consistency
//       hostname: '#{Config.App.FI.Hostname}', // Should be only hostname part of the URL without port
//       momentLocale: '',
//       apiGatewayRegion: 'EU',
//     },
//     gr: {
//       localeCode: 'el-gr', // Keep lowercased version of locale for consistency
//       hostname: '#{Config.App.GR.Hostname}', // Should be only hostname part of the URL without port
//       momentLocale: '',
//       apiGatewayRegion: 'EU',
//     },
//     hu: {
//       localeCode: 'hu-hu', // Keep lowercased version of locale for consistency
//       hostname: '#{Config.App.HU.Hostname}', // Should be only hostname part of the URL without port
//       momentLocale: '',
//       apiGatewayRegion: 'EU',
//     },
//     my: {
//       localeCode: 'en-my', // Keep lowercased version of locale for consistency
//       hostname: '#{Config.App.US.Hostname}', // Should be only hostname part of the URL without port
//       momentLocale: '',
//       apiGatewayRegion: 'EU',
//     },
//     nl: {
//       localeCode: 'nl-nl', // Keep lowercased version of locale for consistency
//       hostname: '#{Config.App.NL.Hostname}', // Should be only hostname part of the URL without port
//       momentLocale: '',
//       apiGatewayRegion: 'EU',
//     },
//     no: {
//       localeCode: 'nb-no', // Keep lowercased version of locale for consistency
//       hostname: '#{Config.App.NO.Hostname}', // Should be only hostname part of the URL without port
//       momentLocale: '',
//       apiGatewayRegion: 'EU',
//     },
//     ph: {
//       localeCode: 'en-ph', // Keep lowercased version of locale for consistency
//       hostname: '#{Config.App.US.Hostname}', // Should be only hostname part of the URL without port
//       momentLocale: '',
//       apiGatewayRegion: 'EU',
//     },
//     th: {
//       localeCode: 'en-th', // Keep lowercased version of locale for consistency
//       hostname: '#{Config.App.US.Hostname}', // Should be only hostname part of the URL without port
//       momentLocale: '',
//       apiGatewayRegion: 'EU',
//     },
//     turkey: {
//       localeCode: 'tr-tr', // Keep lowercased version of locale for consistency
//       hostname: '#{Config.App.TR.Hostname}', // Should be only hostname part of the URL without port
//       momentLocale: '',
//       apiGatewayRegion: 'EU',
//     },
//     ko: {
//       localeCode: 'ko-kr', // Keep lowercased version of locale for consistency
//       hostname: '#{Config.App.KO.Hostname}', // Should be only hostname part of the URL without port
//       momentLocale: '',
//       apiGatewayRegion: 'EU',
//     },
//     it: {
//       localeCode: 'it-it', // Keep lowercased version of locale for consistency
//       hostname: '#{Config.App.IT.Hostname}', // Should be only hostname part of the URL without port
//       momentLocale: '',
//       apiGatewayRegion: 'EU',
//     },
//     jp: {
//       localeCode: 'ja-jp', // Keep lowercased version of locale for consistency
//       hostname: '#{Config.App.JP.Hostname}', // Should be only hostname part of the URL without port
//       momentLocale: '',
//       apiGatewayRegion: 'EU',
//     },
//     ru: {
//       localeCode: 'ru-ru', // Keep lowercased version of locale for consistency
//       hostname: '#{Config.App.RU.Hostname}', // Should be only hostname part of the URL without port
//       momentLocale: '',
//       apiGatewayRegion: 'EU',
//     },
//   },

//   cachedSitecoreApiKey: '#{Config.App.CachedSitecoreApiKey}',
//   uncachedSitecoreApiKey: '#{Config.App.UncachedSitecoreApiKey}',
//   cdnBaseURL: '#{STATIC_CONTENT_PATH}',
//   dleCdnBaseUrl: '#{DLE_STATIC_CONTENT_PATH}',
//   headerFooterPath: '#{Config.App.HeaderFooterPath}',
//   validLocales: [
//     'en-us',
//     'pt-br',
//     'en-africa',
//     'fr-africa',
//     'fr-fr',
//     'en-ca',
//     'fr-ca',
//     'es-pa',
//     'en-in',
//     'sv-se',
//     'de-de',
//     'en-au',
//     'en-gb',
//     'en-sg',
//     'middle-east',
//     'ro-ro',
//     'es-es',
//     'po-pl',
//     'da-dk',
//     'fi-fi',
//     'el-gr',
//     'hu-hu',
//     'en-my',
//     'nl-nl',
//     'nb-no',
//     'en-ph',
//     'en-th',
//     'tr-tr',
//     'ko-kr',
//     'it-it',
//     'ja-jp',
//   ],

//   // Page links
//   accountPageRoute: '#{Config.App.AccountPageUrl}',
//   notificationPageRoute: '#{Config.App.NotificationPageUrl}',
//   settingsPageRoute: '#{Config.App.SettingsPageUrl}',
//   accountSetupPageRoute: '#{Config.App.AccountSetupPageUrl}',
//   avuriRegistrationPageRoute: '#{Config.API.AvuriRegistrationPageUrl}',
//   accountMyTeamPageRoute: '#{Config.API.AccountMyTeamPageUrl}',
//   loginProblemRoute: '#{Config.API.LoginProblemUrl}',
//   errorPages: {
//     error404: '/error-404',
//     error403: '/error-403',
//     error500: '/error-500',
//   },
//   sfdcLoginUrl: '#{Config.App.SfdcLoginUrl}',
//   sfdcLoginReturnUrl: '#{Config.App.SfdcLoginReturnUrl}',
//   sfdcLogoutReturnUrl: '#{Config.App.SfdcLogoutReturnUrl}',
//   sfdcLogoutUrl: '#{Config.App.SfdcLogoutUrl}',
//   launchDarklyClientSideId: '#{Config.App.LaunchDarklyClientId}',
// };
// /**
//  * API specific configuration such as API base url, oAuth client id, api endpoints etc.
//  * Octopus variables related to API should start with #{Config.API.*}
//  * For example: #{Config.API.BASE_URL}
//  */
// const apiConfig = {
//   baseURL: '#{Config.API.BaseURL}',
//   defaultGraphQLUri: '#{Config.API.GraphQLUri}',
//   emeaGraphQLUri: '#{Config.API.EMEA.GraphQLUri}',
//   latamGraphQLUri: '#{Config.API.LATAM.GraphQLUri}',
//   defaultGraphQLTimeout: '#{Config.API.GraphQLTimeout}',
//   paymentEngineEndUrl: '#{Config.API.PaymentEngineUrl}',
//   getPaymentEndpoint: '#{Config.API.GetPaymentEndpoint}',
//   paymentEngineHostId: '#{Config.API.PaymentEngineHostId}',
//   paymentAddCardEndpoint: '#{Config.API.PaymentEngineAddCardEndpoint}',
//   paymentEditCardEndpoint: '#{Config.API.PaymentEngineUpdateCardEndpoint}',
//   sitecoreGraphQLAPIUrl: '#{Config.API.SitecoreGraphQLAPIUrl}',
//   changePasswordURL: '#{Config.SFDC.ChangePassword.URL}',
//   avuriDeviceApplicationId: '#{Config.API.AvuriApplicaitonId}',
//   endpoints: {
//     documentProviderTimeOut: '#{Config.API.DocumentProvider.TimeOut}',
//     documentProviderWebSocketURI: '#{Config.API.DocumentProvider.WebSocketURI}',
//     documentProviderAfricaWebSocketURI: '#{Config.API.DocumentProvider.AFRICA.WebSocketURI}',
//     documentProviderLatamWebSocketURI: '#{Config.API.DocumentProvider.LATAM.WebSocketURI}',
//   },
// };
// export default { ...appConfig, ...apiConfig };

/**
 * Config for Microapp. This will hold the all configuration that microapp needs to run the app.
 *
 * As a good rule of thumb, app related and api related configurations are kept separately.
 */
 
/**
 * Microapp specific configuration. Any microapp octopus variable should start with #{Config.App.*}
 * For example: #{Config.APP.Hostname.US}, #{Config.App.CDN_BASE_URL}
 */
const appConfig = {
  locales: {
    us: {
      localeCode: 'en-us', // Keep lowercased version of locale for consistency
      hostname: 'dev.gehealthcare.com', // Should be only hostname part of the URL without port
      momentLocale: '', // Empty string will not trigger locale update in the application. Will fallback to en locale only.
      apiGatewayRegion: 'US',
    },
    br: {
      localeCode: 'pt-br', // Keep lowercased version of locale for consistency
      hostname: 'dev.gehealthcare.com', // Should be only hostname part of the URL without port
      momentLocale: 'pt-br', // Empty string will not trigger locale update in the application. Will fallback to en locale only.
      apiGatewayRegion: 'LA',
    },
    enza: {
      localeCode: 'en-africa', // Keep lowercased version of locale for consistency
      hostname: '#{Config.App.US.Hostname}', // Should be only hostname part of the URL without port
      momentLocale: '', // Empty string will not trigger locale update in the application. Will fallback to en locale only.
      apiGatewayRegion: 'EU',
    },
    frza: {
      localeCode: 'fr-africa', // Keep lowercased version of locale for consistency
      hostname: '#{Config.App.US.Hostname}', // Should be only hostname part of the URL without port
      momentLocale: 'fr', // Empty string will not trigger locale update in the application. Will fallback to en locale only.
      apiGatewayRegion: 'EU',
    },
    frus: {
      localeCode: 'fr-fr', // Keep lowercased version of locale for consistency
      hostname: 'dev.gehealthcare.com', // Should be only hostname part of the URL without port
      momentLocale: 'fr', // Empty string will not trigger locale update in the application. Will fallback to en locale only.
      apiGatewayRegion: 'EU',
    },
    latam: {
      localeCode: 'es-pa', // Keep lowercased version of locale for consistency
      hostname: 'dev.gehealthcare.com', // Should be only hostname part of the URL without port
      momentLocale: 'es', // Empty string will not trigger locale update in the application. Will fallback to en locale only.
      apiGatewayRegion: 'LA',
    },
    in: {
      localeCode: 'en-in', // Keep lowercased version of locale for consistency
      hostname: '#{Config.App.IN.Hostname}', // Should be only hostname part of the URL without port
      momentLocale: '',
      apiGatewayRegion: 'EU',
    },
    enca: {
      localeCode: 'en-ca', // Keep lowercased version of locale for consistency
      hostname: '#{Config.App.CA.Hostname}', // Should be only hostname part of the URL without port
      apiGatewayRegion: 'US',
    },
    frca: {
      localeCode: 'fr-ca', // Keep lowercased version of locale for consistency
      hostname: '#{Config.App.CA.Hostname}', // Should be only hostname part of the URL without port
      momentLocale: 'fr',
      apiGatewayRegion: 'US',
    },
    // EIPP Specific
    se: {
      localeCode: 'sv-se', // Keep lowercased version of locale for consistency
      hostname: '#{Config.App.SE.Hostname}', // Should be only hostname part of the URL without port
      momentLocale: '',
      apiGatewayRegion: 'EU',
    },
    de: {
      localeCode: 'de-de', // Keep lowercased version of locale for consistency
      hostname: '#{Config.App.DE.Hostname}', // Should be only hostname part of the URL without port
      momentLocale: '',
      apiGatewayRegion: 'EU',
    },
    au: {
      localeCode: 'en-au', // Keep lowercased version of locale for consistency
      hostname: '#{Config.App.AU.Hostname}', // Should be only hostname part of the URL without port
      momentLocale: '',
      apiGatewayRegion: 'EU',
    },
    uk: {
      localeCode: 'en-gb', // Keep lowercased version of locale for consistency
      hostname: '#{Config.App.UK.Hostname}', // Should be only hostname part of the URL without port
      momentLocale: '',
      apiGatewayRegion: 'EU',
    },
    sg: {
      localeCode: 'en-sg', // Keep lowercased version of locale for consistency
      hostname: '#{Config.App.US.Hostname}', // Should be only hostname part of the URL without port
      momentLocale: '',
      apiGatewayRegion: 'EU',
    },
    middleEast: {
      localeCode: 'middle-east', // Keep lowercased version of locale for consistency
      hostname: '#{Config.App.US.Hostname}', // Should be only hostname part of the URL without port
      momentLocale: '',
      apiGatewayRegion: 'EU',
    },
    romania: {
      localeCode: 'ro-ro', // Keep lowercased version of locale for consistency
      hostname: '#{Config.App.RO.Hostname}', // Should be only hostname part of the URL without port
      momentLocale: '',
      apiGatewayRegion: 'EU',
    },
    es: {
      localeCode: 'es-es', // Keep lowercased version of locale for consistency
      hostname: '#{Config.App.ES.Hostname}', // Should be only hostname part of the URL without port
      momentLocale: '',
      apiGatewayRegion: 'EU',
    },
    pl: {
      localeCode: 'po-pl', // Keep lowercased version of locale for consistency
      hostname: '#{Config.App.PO.Hostname}', // Should be only hostname part of the URL without port
      momentLocale: '',
      apiGatewayRegion: 'EU',
    },
    dk: {
      localeCode: 'da-dk', // Keep lowercased version of locale for consistency
      hostname: '#{Config.App.DK.Hostname}', // Should be only hostname part of the URL without port
      momentLocale: '',
      apiGatewayRegion: 'EU',
    },
    fi: {
      localeCode: 'fi-fi', // Keep lowercased version of locale for consistency
      hostname: '#{Config.App.FI.Hostname}', // Should be only hostname part of the URL without port
      momentLocale: '',
      apiGatewayRegion: 'EU',
    },
    gr: {
      localeCode: 'el-gr', // Keep lowercased version of locale for consistency
      hostname: '#{Config.App.GR.Hostname}', // Should be only hostname part of the URL without port
      momentLocale: '',
      apiGatewayRegion: 'EU',
    },
    hu: {
      localeCode: 'hu-hu', // Keep lowercased version of locale for consistency
      hostname: '#{Config.App.HU.Hostname}', // Should be only hostname part of the URL without port
      momentLocale: '',
      apiGatewayRegion: 'EU',
    },
    my: {
      localeCode: 'en-my', // Keep lowercased version of locale for consistency
      hostname: '#{Config.App.US.Hostname}', // Should be only hostname part of the URL without port
      momentLocale: '',
      apiGatewayRegion: 'EU',
    },
    nl: {
      localeCode: 'nl-nl', // Keep lowercased version of locale for consistency
      hostname: '#{Config.App.NL.Hostname}', // Should be only hostname part of the URL without port
      momentLocale: '',
      apiGatewayRegion: 'EU',
    },
    no: {
      localeCode: 'nb-no', // Keep lowercased version of locale for consistency
      hostname: '#{Config.App.NO.Hostname}', // Should be only hostname part of the URL without port
      momentLocale: '',
      apiGatewayRegion: 'EU',
    },
    ph: {
      localeCode: 'en-ph', // Keep lowercased version of locale for consistency
      hostname: '#{Config.App.US.Hostname}', // Should be only hostname part of the URL without port
      momentLocale: '',
      apiGatewayRegion: 'EU',
    },
    th: {
      localeCode: 'en-th', // Keep lowercased version of locale for consistency
      hostname: '#{Config.App.US.Hostname}', // Should be only hostname part of the URL without port
      momentLocale: '',
      apiGatewayRegion: 'EU',
    },
    turkey: {
      localeCode: 'tr-tr', // Keep lowercased version of locale for consistency
      hostname: '#{Config.App.TR.Hostname}', // Should be only hostname part of the URL without port
      momentLocale: '',
      apiGatewayRegion: 'EU',
    },
    ko: {
      localeCode: 'ko-kr', // Keep lowercased version of locale for consistency
      hostname: '#{Config.App.KO.Hostname}', // Should be only hostname part of the URL without port
      momentLocale: '',
      apiGatewayRegion: 'EU',
    },
    it: {
      localeCode: 'it-it', // Keep lowercased version of locale for consistency
      hostname: '#{Config.App.IT.Hostname}', // Should be only hostname part of the URL without port
      momentLocale: '',
      apiGatewayRegion: 'EU',
    },
    jp: {
      localeCode: 'ja-jp', // Keep lowercased version of locale for consistency
      hostname: '#{Config.App.JP.Hostname}', // Should be only hostname part of the URL without port
      momentLocale: '',
      apiGatewayRegion: 'EU',
    },
    ru: {
      localeCode: 'ru-ru', // Keep lowercased version of locale for consistency
      hostname: '#{Config.App.RU.Hostname}', // Should be only hostname part of the URL without port
      momentLocale: '',
      apiGatewayRegion: 'EU',
    },
  },
  validLocales: [
    'en-us',
    'pt-br',
    'en-africa',
    'fr-africa',
    'fr-fr',
    'en-ca',
    'fr-ca',
    'es-pa',
    'en-in',
    'sv-se',
    'de-de',
    'en-au',
    'en-gb',
    'en-sg',
    'middle-east',
    'ro-ro',
    'es-es',
    'po-pl',
    'da-dk',
    'fi-fi',
    'el-gr',
    'hu-hu',
    'en-my',
    'nl-nl',
    'nb-no',
    'en-ph',
    'en-th',
    'tr-tr',
    'ko-kr',
    'it-it',
    'ja-jp',
    'ru-ru',
  ],
  // Page links
  cachedSitecoreApiKey: '{D24C87E5-C517-4DA1-8FD3-031439D00014}',
  uncachedSitecoreApiKey: '{337C884C-2BD3-4CFA-BB4B-56410974C0C4}',
  cdnBaseURL: 'https://dev.gehealthcare.com/accountcdn',
  dleCdnBaseUrl: 'https://dev.gehealthcare.com/accountcdn', // DLE CDN Base URL - using same as cdnBaseURL for dev
  headerFooterPath: '/headerfooter',
  // validLocales: ['en-us', 'pt-br', 'es-pa', 'en-africa', 'fr-fr', 'fr-africa'],
  // Page links
  accountPageRoute: 'account',
  settingsPageRoute: 'account/settings', // #{Config.App.SettingsPageUrl}
  notificationPageRoute: '#{Config.App.NotificationPageUrl}',
  accountSetupPageRoute: '#{Config.App.AccountSetupPageUrl}',
  avuriRegistrationPageRoute: '#{Config.API.AvuriRegistrationPageUrl}',
  accountMyTeamPageRoute: '#{Config.API.AccountMyTeamPageUrl}',
  loginProblemRoute: '#{Config.API.LoginProblemUrl}',
  errorPages: {
    error404: '/error-404',
    error403: '/error-403',
    error500: '/error-500',
  },
  sfdcLoginUrl: '#{Config.App.SfdcLoginUrl}',
  sfdcLoginReturnUrl: '#{Config.App.SfdcLoginReturnUrl}',
  sfdcLogoutReturnUrl: '#{Config.App.SfdcLogoutReturnUrl}',
  sfdcLogoutUrl: '#{Config.App.SfdcLogoutUrl}',
  launchDarklyClientSideId: '#{Config.App.LaunchDarklyClientId}',
};
 
/**
 * API specific configuration such as API base url, oAuth client id, api endpoints etc.
 * Octopus variables related to API should start with #{Config.API.*}
 * For example: #{Config.API.BASE_URL}
 */
const apiConfig = {
  baseURL: '#{Config.API.BaseURL}',
  defaultGraphQLUri: 'https://cx-us-nprd-services.cloud.gehealthcare.com/us-ver-shared-services-cdx-api-gateway',
  emeaGraphQLUri: 'https://cx-eu-nprd-services.cloud.gehealthcare.com/eu-ver-shared-services-cdx-api-gateway',
  defaultGraphQLTimeout: '30000',
  paymentEngineEndUrl: 'https://stg-payment.gehealthcare.com/gehc/gehcpaymentwebservices/v2',
  getPaymentEndpoint: 'getPaymentDetails',
  paymentEngineHostId: 'mygehc',
  paymentAddCardEndpoint: 'https://stg-payment.gehealthcare.fr/pay/create',
  paymentEditCardEndpoint: 'https://stg-payment.gehealthcare.fr/pay/update',
  sitecoreGraphQLAPIUrl:
    'https://qa.gehealthcare.com/sitecore/api/graph/GEHCGraphQLService?sc_apikey=%7BD24C87E5-C517-4DA1-8FD3-031439D00014%7D',
  endpoints: {
    documentProviderTimeOut: '#{Config.API.DocumentProvider.TimeOut}',
    documentProviderWebSocketURI: '#{Config.API.DocumentProvider.WebSocketURI}',
    documentProviderAfricaWebSocketURI: '#{Config.API.DocumentProvider.AFRICA.WebSocketURI}',
    documentProviderLatamWebSocketURI: '#{Config.API.DocumentProvider.LATAM.WebSocketURI}',
  },
};
export default { ...appConfig, ...apiConfig };
