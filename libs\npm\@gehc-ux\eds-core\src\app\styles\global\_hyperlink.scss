/**
 *  hyperlink.scss
 *
 *  Copyright (c) 2019 by General Electric Company. All rights reserved.
 *
 *  The copyright to the computer software herein is the property of
 *  General Electric Company. The software may be used and/or copied only
 *  with the written permission of General Electric Company or in accordance
 *  with the terms and conditions stipulated in the agreement/contract
 *  under which the software has been supplied.
 *
 * @main
 * @name Hyperlink
 *
 * @description
 * Use a hyperlink when linking to another document or URL. Meaningful link text
 * makes pages more friendly for visitors and search engines. The link text
 * should always describe what the user will see when they click on it. It
 * should never be "Click Here", "Here", or the URL itself.
 *
 * @browsers chrome, safari, firefox, ie11, edge
 * @workinprogress true
 */

@import 'node_modules/sass-bem/bem';

a {
  color: var(--eds-links);
  text-decoration: none;

  &:hover,
  &:focus {
    color: var(--eds-links-hover);
    text-decoration: underline;
  }

  &:active {
    color: var(--eds-links-active);
    text-decoration: underline;
  }

  &.hyperlink__icon {
    display: flex;
    align-items: center;
    fill: currentColor;
  }

  &.hyperlink__icon--right {
    > span {
      margin-right: $eds-base-spacing-unit--tiny;
    }
  }

  &.hyperlink__icon--left {
    > span {
      margin-left: $eds-base-spacing-unit--tiny;
    }
  }

  // Disabled link
  &:not([href]),
  &.hyperlink--disabled {
    color: var(--eds-links-disabled);
    cursor: not-allowed;
    pointer-events: none;
  }

} // a

/**
 * link--secondary modifier class for secondary a link color
 */
@include block('link') {
  @include modifier('secondary') {
    color: var(--eds-links-secondary);
    text-decoration: none;

    &:hover,
    &:focus {
      color: var(--eds-links-secondary-hover);
    }

    &:active {
      color: var(--eds-links-secondary-active);
    }
  }
}

/**
  * hyperlink-neutral Class for neutral hyperlink
  */  
  @include block('hyperlink-neutral') {
  
    text-decoration: underline;
    color: var(--eds-hyperlink-neutral-text-color);
      
    &:hover,
    &:focus {
      color: var(--eds-hyperlink-neutral-text-hover-color);
    }
  
    &:active {
      color: var(--eds-hyperlink-neutral-text-active-color);
    }
  
    // Disabled link
    &:not([href]),
    &.hyperlink-neutral--disabled {
      color: var(--eds-hyperlink-neutral-disabled-text-color);
      text-decoration: none;
      cursor: not-allowed;
      pointer-events: none;
    }
  
    /**
      * hyperlink-neutral--inverse Class for neutral inverse hyperlink
      */  
    @include modifier('inverse') {
      text-decoration: underline;
      color: var(--eds-hyperlink-neutral-inverse-text-color);
      
      &:hover,
      &:focus {
        color: var(--eds-hyperlink-neutral-inverse-text-hover-color);
      }
  
      &:active {
        color: var(--eds-hyperlink-neutral-inverse-text-active-color);
      }

      // Disabled link
      &:not([href]),
      &.hyperlink-neutral--disabled {
        color: var(--eds-hyperlink-neutral-disabled-text-color);
        text-decoration: none;
        cursor: not-allowed;
        pointer-events: none;
      }
    }
  }