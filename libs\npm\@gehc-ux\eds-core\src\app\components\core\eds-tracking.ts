/**
 *  eds-tracking.ts
 *
 *  Copyright (c) 2018 by General Electric Company. All rights reserved.
 *
 *  The copyright to the computer software herein is the property of
 *  General Electric Company. The software may be used and/or copied only
 *  with the written permission of General Electric Company or in accordance
 *  with the terms and conditions stipulated in the agreement/contract
 *  under which the software has been supplied.
 *
 * Created by ********* on 2018-09-18.
 */
declare global {
  interface Window {
    GEHC: any;
  }
}

import { OnInit, AfterViewInit } from '@angular/core';
import { ComponentTrackingData, GlobalTracking } from './tracking-global';

export abstract class EDSTracking implements OnInit, AfterViewInit {

  constructor(componentName: string) {

    if (!window.GEHC || !window.GEHC.EDS || !window.GEHC.EDS.trackingPrefs) {
      GlobalTracking.initGlobal();
    }

    this.componentName = componentName;
  }

  protected componentName: string;
  protected getTrackingPrefs;

  protected performanceStartTimestamp: number;

  ngOnInit() {
    if (window.GEHC.EDS.trackingPrefs.trackPerformance) {
      this.performanceStartTimestamp = performance.now();
    }
  }

  ngAfterViewInit() {
    const trackOptions: ComponentTrackingData = {
      name: this.componentName,
      action: 'initialized',
      version: window.GEHC.EDS.components[this.componentName] ? window.GEHC.EDS.components[this.componentName].version : undefined
    };

    if (window.GEHC.EDS.trackingPrefs.trackPerformance && this.performanceStartTimestamp) {
      const end = performance.now();
      const elapsed = end - this.performanceStartTimestamp;
      trackOptions.loadTime = elapsed;
    }
    this.track(trackOptions);
  }

  /**
   * track data for component to provide usage analytics.
   *
   * @param componentData should have any data of interest from component
   * , must provide `componentName` property to properly post.
   */
  protected track(componentData: ComponentTrackingData): any {
    if (!componentData) {
      throw new Error('required parameter `componentData` missing.');
    }
    if (!componentData.name) {
      throw new Error(
        'required parameter property `componentData.name` missing.'
      );
    }

    if (window.GEHC.EDS.trackingPrefs.trackUsage) {
      window.GEHC.EDS.tracking.track(componentData);
    }
  }
}
