import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import HeaderControl from '../Component';
import { HeaderFields } from '../models';

// Mock the useHeaderFooter hook
jest.mock('../../../hooks', () => ({
  useHeaderFooter: jest.fn(),
}));

// Mock the HeaderRedesignComponent
jest.mock('../components/HeaderRedesignComponent', () => ({
  HeaderRedesignComponent: jest.fn(({ fields, componentName, keywords }) => (
    <div data-testid="header-redesign-component">
      <div data-testid="header-component-name">{componentName}</div>
      <div data-testid="header-site-logo">{fields?.siteLogo?.value?.alt}</div>
      <div data-testid="header-search-placeholder">{keywords?.placeholderValue}</div>
      <div data-testid="header-my-account-title">{fields?.myAccountTitle?.value}</div>
    </div>
  )),
}));

// Mock the ImpersonationRibbon component
jest.mock('cx-dle-component-library', () => ({
  ImpersonationRibbon: jest.fn(({ user, ribbonMessageKey }) => (
    <div data-testid="impersonation-ribbon">
      <div data-testid="ribbon-user">{user?.name}</div>
      <div data-testid="ribbon-message">{ribbonMessageKey}</div>
    </div>
  )),
}));

// Mock react-i18next
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key: string) => {
      const translations: { [key: string]: string } = {
        'Navigation.Search.DesktopProminentSearch': 'Search products, services, and more...',
        'Navigation.Search.SuggestedKeywords': 'Suggested Keywords',
        'Navigation.Search.RecentSearches': 'Recent Searches',
        'Navigation.Search.QuickLinks': 'Quick Links',
        'Navigation.Search.GoToSite': 'Go to Site',
        'Navigation.Search.ServiceShop': 'Service Shop',
        'Navigation.Search.Explore': 'Explore',
        'Navigation.Search.Shop': 'Shop',
        'Navigation.Search.ServiceShopURL': '/service-shop',
        'CSM.EditorRibbonMessage': 'You are viewing as an editor',
        'CSM.ReadOnlyRibbonMessage': 'You are viewing in read-only mode',
        'CSM.ChangeCustomer': 'Change Customer',
      };
      return translations[key] || key;
    },
  }),
}));

// Mock react-cookie
jest.mock('react-cookie', () => ({
  Cookies: jest.fn().mockImplementation(() => ({
    get: jest.fn((name: string) => {
      if (name === 'id_token') return 'mock-token';
      if (name === 'CSM_ADMIN_COOKIE') return 'admin-value';
      return null;
    }),
  })),
}));

// Mock utils
jest.mock('../../../utils', () => ({
  isCSMAdminMode: jest.fn(() => false),
}));

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

const mockHeaderData: HeaderFields = {
  id: 'header-1',
  name: 'Main Header',
  disableAuthenticationLinks: {
    value: false
  },
  siteLogoLink: {
    value: {
      href: '/',
      text: 'Home',
      anchor: '',
      linktype: 'internal'
    }
  },
  accountNavigationList: {
    id: 'account-nav-1',
    url: '/account',
    fields: {
      id: 'account-nav-1',
      name: 'Account Navigation',
      hoverDelay: {
        value: '300'
      },
      welcomeTitle: {
        value: 'Welcome'
      },
      manageYourAccountLink: {
        value: {
          href: '/account/manage',
          text: 'Manage Your Account',
          anchor: '',
          linktype: 'internal'
        }
      },
      loggedInMessage: {
        value: 'You are logged in'
      }
    }
  },
  siteLogo: {
    value: {
      src: '/logo.png',
      alt: 'Company Logo',
      width: '144',
      height: '32'
    }
  },
  mobileSearchHeaderLogo: {
    value: {
      src: '/mobile-logo.png',
      alt: 'Mobile Logo',
      width: '48',
      height: '48'
    }
  },
  siteCDNLogo: {
    value: 'https://cdn.example.com/logo.png'
  },
  myAccountTitle: {
    value: 'My Account'
  },
  enableProminentSearch: {
    value: true
  },
  disableSignIn: {
    value: false
  },
  disableRegister: {
    value: false
  },
  disableSearch: {
    value: false
  },
  isRedesignHeader: {
    value: true
  },
  dontHaveAccount: {
    value: "Don't have an account?"
  },
  secondaryNavigationLinks: [],
  signInStartAnalyticsEvent: {
    value: 'sign_in_start'
  },
  includeCartLogo: {
    value: true
  },
  cartIcon: {
    value: {
      src: '/cart-icon.png',
      alt: 'Cart',
      width: '24',
      height: '24'
    }
  },
  hoverDelay: {
    value: '300'
  },
  cartQuantity: '0',
  cartPageUrl: '/cart',
  customData: {
    primaryNavigationLinks: [
      {
        title: 'Products',
        generalLink: {
          value: {
            href: '/products',
            text: 'Products',
            anchor: '',
            linktype: 'internal'
          }
        },
        category: 'main',
        menuLinks: [],
        promo: null
      }
    ],
    searchPageUrl: '/search',
    bookmarkPageUrl: '/bookmarks',
    bookmarkPageText: 'Bookmarks',
    authenticationLinks: [
      {
        generalLink: {
          value: {
            href: '/login',
            text: 'Sign In',
            anchor: '',
            linktype: 'internal'
          }
        },
        title: 'Sign In',
        analyticsEvent: 'sign_in_click',
        icon: null,
        showNotificationIndicator: false,
        accountNavigationLinks: null
      }
    ]
  }
};

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('Header Component', () => {
  const mockUseHeaderFooter = require('../../../hooks').useHeaderFooter;
  const mockIsCSMAdminMode = require('../../../utils').isCSMAdminMode;

  beforeEach(() => {
    jest.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue(null);
    mockIsCSMAdminMode.mockReturnValue(false);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders header with redesign component when data is available and redesign is enabled', () => {
    mockUseHeaderFooter.mockReturnValue({
      headerData: mockHeaderData,
      error: null,
    });

    render(<HeaderControl />, { wrapper: createWrapper() });

    expect(screen.getByTestId('header-redesign-component')).toBeInTheDocument();
    expect(screen.getByTestId('header-component-name')).toHaveTextContent('Header');
    expect(screen.getByTestId('header-site-logo')).toHaveTextContent('Company Logo');
    expect(screen.getByTestId('header-search-placeholder')).toHaveTextContent('Search products, services, and more...');
    expect(screen.getByTestId('header-my-account-title')).toHaveTextContent('My Account');
  });

  it('does not render redesign component when redesign is disabled', () => {
    const headerDataWithoutRedesign = {
      ...mockHeaderData,
      isRedesignHeader: {
        value: false
      }
    };

    mockUseHeaderFooter.mockReturnValue({
      headerData: headerDataWithoutRedesign,
      error: null,
    });

    render(<HeaderControl />, { wrapper: createWrapper() });

    expect(screen.queryByTestId('header-redesign-component')).not.toBeInTheDocument();
  });

  it('displays error message when there is an error', () => {
    mockUseHeaderFooter.mockReturnValue({
      headerData: null,
      error: new Error('Failed to fetch header data'),
    });

    render(<HeaderControl />, { wrapper: createWrapper() });

    expect(screen.getByText('Error loading header')).toBeInTheDocument();
    expect(screen.queryByTestId('header-redesign-component')).not.toBeInTheDocument();
  });

  it('renders impersonation ribbon when user data is available', () => {
    const mockUserData = {
      user: { name: 'John Doe' },
      searchURL: '/search'
    };

    mockLocalStorage.getItem.mockReturnValue(JSON.stringify(mockUserData));
    mockUseHeaderFooter.mockReturnValue({
      headerData: mockHeaderData,
      error: null,
    });

    render(<HeaderControl />, { wrapper: createWrapper() });

    expect(screen.getByTestId('impersonation-ribbon')).toBeInTheDocument();
    expect(screen.getByTestId('ribbon-user')).toHaveTextContent('John Doe');
    expect(screen.getByTestId('ribbon-message')).toHaveTextContent('You are viewing in read-only mode');
  });

  it('shows editor ribbon message when in CSM admin mode', () => {
    const mockUserData = {
      user: { name: 'Admin User' },
      searchURL: '/search'
    };

    mockLocalStorage.getItem.mockReturnValue(JSON.stringify(mockUserData));
    mockIsCSMAdminMode.mockReturnValue(true);
    mockUseHeaderFooter.mockReturnValue({
      headerData: mockHeaderData,
      error: null,
    });

    render(<HeaderControl />, { wrapper: createWrapper() });

    expect(screen.getByTestId('impersonation-ribbon')).toBeInTheDocument();
    expect(screen.getByTestId('ribbon-message')).toHaveTextContent('You are viewing as an editor');
  });

  it('does not render impersonation ribbon when no user data is available', () => {
    mockLocalStorage.getItem.mockReturnValue(null);
    mockUseHeaderFooter.mockReturnValue({
      headerData: mockHeaderData,
      error: null,
    });

    render(<HeaderControl />, { wrapper: createWrapper() });

    expect(screen.queryByTestId('impersonation-ribbon')).not.toBeInTheDocument();
  });

  it('handles null header data gracefully', () => {
    mockUseHeaderFooter.mockReturnValue({
      headerData: null,
      error: null,
    });

    render(<HeaderControl />, { wrapper: createWrapper() });

    expect(screen.queryByTestId('header-redesign-component')).not.toBeInTheDocument();
    expect(screen.queryByText('Error loading header')).not.toBeInTheDocument();
  });

  it('renders with correct header structure and classes', () => {
    mockUseHeaderFooter.mockReturnValue({
      headerData: mockHeaderData,
      error: null,
    });

    const { container } = render(<HeaderControl />, { wrapper: createWrapper() });

    const headerElement = container.querySelector('header.ge-page-header-V2');
    expect(headerElement).toBeInTheDocument();
  });

  it('passes correct keywords to HeaderRedesignComponent', () => {
    mockUseHeaderFooter.mockReturnValue({
      headerData: mockHeaderData,
      error: null,
    });

    render(<HeaderControl />, { wrapper: createWrapper() });

    expect(screen.getByTestId('header-search-placeholder')).toHaveTextContent('Search products, services, and more...');
  });
});
