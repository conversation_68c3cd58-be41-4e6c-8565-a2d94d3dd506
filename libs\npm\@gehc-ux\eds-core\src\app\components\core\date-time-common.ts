/**
 *  date-time-common.ts
 *
 *  Copyright (c) 2018 by General Electric Company. All rights reserved.
 *
 *  The copyright to the computer software herein is the property of
 *  General Electric Company. The software may be used and/or copied only
 *  with the written permission of General Electric Company or in accordance
 *  with the terms and conditions stipulated in the agreement/contract
 *  under which the software has been supplied.
 *
 * Created by ********* on 2018-11-02.
*/
'use strict';

import * as moment from 'moment-timezone';
import { EDSComponent } from './eds-component';
import { ElementRef, Input, ViewChild, HostListener, ChangeDetectorRef} from '@angular/core';


/**
 * Declaration for TypeScript to allow us to reference `window.GEHC` object.
 */
declare global {
  interface Window { GEHC: any; }
}

/**
 *  Superclass for DatePicker and TimePicker components
 */
abstract class EDSDateTime extends EDSComponent {

  @ViewChild('selectTriggerButton') selectTriggerButton;
  @ViewChild('inputContainer') inputContainer;

  /**
   * @constructor Constructor which must be called by implementing derived classes.
   * @param element The component ElementRef
   * @param componentName The component name
   * @param config Configuration specific to each subclass. Must contain
   * these two properties:
   * ```
   *    {
   *      valueChangedEvent: string,  // the name of the event which will be fired when date/time object is changed
   *      format: string // the moment.js format string to represent the date/time
   *    }
   * ```
   */
  constructor(element: ElementRef, componentName: string, config: DateTimeConfig) {
    super(element, componentName);
    if (!config.valueChangedEvent) {
      throw new Error('Required config parameter `valueChangedEvent` not provided.');
    }
    if (!config.format) {
      throw new Error('Required config parameter `format` not provided.');
    }
    this.valueChangedEvent = config.valueChangedEvent;
    this.format = config.format;

    // expose moment for people to use, notably if they want to pass a moment object to a picker
    window.GEHC = window.GEHC || {};
    window.GEHC.EDS = window.GEHC.EDS || {};
    window.GEHC.EDS.moment = moment;

  }

  /**
   * The name of the event that will be fired with the date or time object is changed.
   */
  valueChangedEvent: string;

  /**
   * Moment.js object describing the date/time reprenseted by the picker component.
   * more information: https://momentjs.com/docs
   */
  @Input() momentObj: moment.Moment = null;

  /**
   * Moment.js format to use to display the date / time
   * More information on available formats: https://momentjs.com/docs/#/displaying/format/.
   *
   * Moment supports a lot of different formats, feel free to experiment with them. However
   * this datepicker will only be tested and validated against a few of the most common format:
   *  - date:
   *    - days: `DD` or `D`
   *    - months: `MM` or `M`
   *    - years: `YY` or `YYYY`
   *  - time:
   *    - hours:
   *      - `H` or `HH` for one- or two-digit 24 hour clock
   *      - `h` or `hh` for one- or two-digit 12 hour clock
   *    - minutes:
   *      - `m` or `mm` for one- or two-digit minutes
   *
   * Any symbol can be used between the formats but the two symbols should match
   */
  format: string;

  /**
   * moment object used to rollback changes when cancelling an edit
   */
  previousMomentObj: moment.Moment = null;

  /**
   * String representing the human-readable formatted date / time
   */
  formattedDisplay: string;

  /**
   * Used to turn on "error state" after validation
   */
  inputError: boolean = false;

  /**
   * Whether the dropdowns controls representing day/month/year/hours/minutes/period are currently shown
   */
  isSelectionEditMode: boolean = false;

  /**
   * Whether the input fields representing day/month/year/hours/minutes/period are currently shown
   */
  isInlineEditMode: boolean = false;

  /**
   * User-friendly place holder string to suggest format
   * when there is no momentObj value set. Used in lieu of
   * programmer format which is required by moment API.
   */
  protected abstract placeHolderFormat: string;

  /**
   * changeDetector reference for derived classes.
   */
  protected abstract changeDetector: ChangeDetectorRef;

  /**
   * Called from selectionEditModeStart().
   * Derived classes must implement the logic to initialze the selection controls
   */
  abstract setupSelectControls(): void;

  /**
   * Called from inlineEditModeStart().
   * Derived classes must implement the logic to set up the input
   * fields
   */
  abstract setupInputFields(): void;

  /** Called from onInputEnter. Derived
   *  classes must implement the logic to validate input fields.
   *  If valid, the implementation should fire the component's value
   *  changed event. If invalid, it should nullify this.momentObj, then
   *  call inlineEditModeEnd()
   */
  abstract validateInputs(): void;

  /**
   *
   * Click event when select trigger button is clicked
   */
  onSelectTriggerButtonClick(event: MouseEvent) {
    event.stopImmediatePropagation();
    if (this.isSelectionEditMode) {
      this.selectionEditModeEnd(true);
    } else {
      this.selectionEditModeStart();
    }
    this.isInlineEditMode = false;
    this.changeDetector.detectChanges();
  }

  /**
   * Opens the edit mode with select list controls
   */
  selectionEditModeStart(): void {

    // store current moment
    this.previousMomentObj = this.momentObj ? this.momentObj.clone() : null;
    this.inputError = false;

    // if we don't have a moment initialize it to something so that we can select values
    // in the dropdown
    this.momentObj = this.momentObj || moment();

    this.setupSelectControls();

    this.isSelectionEditMode = true;
  }

  /**
   * Closes the dropdowns and optionally rollback moment if cancelling
   * @param cancel: boolean optional parameter to cancel the edits and
   * reset momentObj to the value before edits.
  */
  selectionEditModeEnd(cancel?: boolean) {
    if (this.isSelectionEditMode) {
      this.isSelectionEditMode = false;

      if (cancel) {
        this.momentObj = this.previousMomentObj ? this.previousMomentObj.clone() : null;
      }

      this.formatDisplay();

      if (!cancel) {
        this.fire(this.valueChangedEvent, {dateTime: this.momentObj, formattedValue: this.formattedDisplay});
      }

      this.selectTriggerButton.nativeElement.focus();
    }
  }

  /**
   * Formats the text display based on current `momentObj` and `format`
   */
  formatDisplay() {
    this.formattedDisplay = this.momentObj ?
      this.momentObj.format(this.format) :
      this.placeHolderFormat;
  }

  /**
   * Switches UI to present input fields for date / time fields
   * calls `setupInputFields` which derived classes must implement.
   */
  inlineEditModeStart() {
    this.isInlineEditMode = true;
    this.selectionEditModeEnd(true);
    this.previousMomentObj = this.momentObj ? this.momentObj.clone() : null;
    this.setupInputFields();
  }

  /**
   * Ends input field mode by hiding all inputs and formatting our
   * current date / time
   */
  inlineEditModeEnd() {
    this.isInlineEditMode = false;
    this.formatDisplay();
    this.fire(this.valueChangedEvent, {dateTime: this.momentObj, formattedValue: this.formattedDisplay});
  }

 /**
   * Read all inputs and try to validate them against the current format. If invalid set the
   * moment object to null and sets the error mode on
   */
  inputContainerKeyup(event: KeyboardEvent) {
    if (event.target === this.inputContainer.nativeElement) {
      // start editing on enter or numbers
      if (event.key === 'Enter' || event.key.match(/[0-9]/)) {
        this.inlineEditModeStart();
      }
    }
  }

  /**
   * Triggers when enter is pressed on keyboard on inline edit input fields
   */
  onInputEnter() {
    this.validateInputs();
  }

  /**
   * Cancels editing and rolls back momentObj to previous value
   */
  cancelEditing() {
    if (this.isInlineEditMode || this.isSelectionEditMode) {
      this.momentObj = this.previousMomentObj ? this.previousMomentObj.clone() : null;
      if (this.isInlineEditMode) {
        this.inputContainer.nativeElement.focus();
      }

      if (this.isSelectionEditMode) {
        this.selectTriggerButton.nativeElement.focus();
      }

      this.isInlineEditMode = false;
      this.isSelectionEditMode = false;

      this.formatDisplay();
    }
  }

  /**
   * Focuses an input and selects all texts it contains
   */
  focusInput(input) {
    input.nativeElement.focus();
    input.nativeElement.setSelectionRange(0, input.nativeElement.value.length);
  }

  /**
   * Detecting outside click to close dropdown/inputs editing
   * @param event
   */
  @HostListener('document:click', ['$event'])
  clickout(event) {
    if (event.path.indexOf(this.hostRef) === -1) {
      this.cancelEditing();
    }
  }

}

/**
 * Interface for config object of constructor
 */
interface DateTimeConfig {
  valueChangedEvent: string;
  format: string;
}

/**
 * Public API
 */

// TODO: REMOVE DateTimeUtil WHEN EDSDatePicker IS REFACTORED
// DateTimeUtil WAS REMOVED ON EDSTimePicker WAS REFACTORED; LEFT HERE TO
// MINIMALLY IMPACT EDSDatePicker
abstract class DateTimeUtil {}
export {
  DateTimeUtil,
  EDSDateTime
};
