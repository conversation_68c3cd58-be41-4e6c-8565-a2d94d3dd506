declare global {
  interface Window {
    GEHC: any;
  }
}

export class GlobalTracking {


  currentData: TrackingData = new TrackingData();
  debounceTiming = 200;
  currentHash = '';
  trackTimeout;
  isDebouncing = false;

  constructor() {
    this.currentData = this.initializeData();
  }

  static initGlobal() {
    window.GEHC = window.GEHC || {};
    window.GEHC.EDS = window.GEHC.EDS || {};
    window.GEHC.EDS.components = window.GEHC.EDS.components || {};

    // static config comes from build; if other components have already
    // read static config, or user has overriden any values, take those
    // as precendence (trackingPrefs from global namespace)
    const trackingConfig = require('../components-tracking.json');
    window.GEHC.EDS.trackingPrefs = Object.assign(trackingConfig, window.GEHC.EDS.trackingPrefs);

    window.GEHC.EDS.tracking = window.GEHC.EDS.tracking || new GlobalTracking();

    // reinitialize current data to make sure org and app name match
    window.GEHC.EDS.tracking.currentData = window.GEHC.EDS.tracking.initializeData();
  }

  track(componentData: ComponentTrackingData) {

    // track "by page", when changing page reset the tracking object
    if (window.location.hash !== this.currentHash) {

      // if we have a current debounce running post that and restart
      if (this.isDebouncing) {

        clearTimeout(this.trackTimeout);
        this.processData();
      }

      this.currentHash = window.location.hash;
      this.currentData = this.initializeData();
    }

    // keep building the object with new information
    this.addComponentData(componentData);

    // debounce calls to the post
    clearTimeout(this.trackTimeout);
    this.isDebouncing = true;
    this.trackTimeout = setTimeout(this.processData.bind(this), this.debounceTiming);
  }

  /**
   * Processes the current data appropriately by posting it and logging if needed. Resets debounce.
   */
  processData() {
    this.isDebouncing = false;

    if (window.GEHC.EDS.trackingPrefs.browserDebug) {
      this.logCurrentData();
    }
    this.postCurrentData();
  }

  /**
   * submit post request to backend service.
   * @param data composed data to be persisted.
   */
  postCurrentData() {

    if (!window.GEHC.EDS.trackingPrefs.disableNetworkPost) {
      fetch(`${window.GEHC.EDS.trackingPrefs.server}/v1/components`, {
        method: 'post',
        credentials: 'omit', // include, same-origin, *omit
        mode: 'cors',
        cache: 'no-cache',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': 'test'
        },
        body: JSON.stringify(this.currentData)
      });
    }
  }

  /**
   * Builds a basic data tracking object that can be augmented later
   */
  initializeData() {
    return new TrackingData({
      browser: navigator.userAgent,
      pageView: `${document.location.pathname}${document.location.hash}`,
      organization: window.GEHC.EDS.trackingPrefs.organization,
      applicationName: window.GEHC.EDS.trackingPrefs.applicationName,
      components: {}
    });
  }

  /**
   * Adds some component data to our tracking object
   * @param componentData
   */
  addComponentData(componentData: ComponentTrackingData) {

    if (!this.currentData) {
      this.currentData = this.initializeData();
    }

    if (!this.currentData.components[componentData.name]) {

      // build this comp data for the first time on this page
      this.currentData.components[componentData.name] = {
        action: componentData.action,
        loadTime: componentData.loadTime ? [componentData.loadTime] : [],
        version: componentData.version,
        count: 1
      };
    } else {

      // keep counting this component
      this.currentData.components[componentData.name].count++;
      if (componentData.loadTime) {
        this.currentData.components[componentData.name].loadTime.push(componentData.loadTime);
      }
    }
  }

  /**
   * Logs the current data that has been build for the current location hash
   */
  logCurrentData() {

    // directly output the object for better inspection
    console.log(this.currentData);
  }
}

export interface ComponentTrackingData {
  name: string;
  action: string;
  version: string;
  loadTime?: number;
}

interface ComponentTrackingDataAggregate {
  action: string;
  version: string;
  count: number;
  loadTime: number[];
}

class TrackingData {
  constructor (options?: any) {
    Object.assign(this, options);
  }
  browser: string;
  pageView: string;
  organization: string;
  applicationName: string;
  components: {[key: string]: ComponentTrackingDataAggregate};
}
