$eds-checkbox-font-size: $eds-large-font-size;
$eds-checkbox-helper-font-size: $eds-medium-font-size;
$eds-checkbox-margin-right: calculateRem(10px);
$eds-checkbox-helper-max-width: calculateRem(240px);
$eds-checkbox-text-adjustment: calculateRem(-3px);
$eds-checkbox-helper-adjustment: calculateRem(7px);

$eds-checkbox-box-width: calculateRem(16px);
$eds-checkbox-box-height: calculateRem(16px);
$eds-checkbox-box-width-responsive: calculateRem(24px);
$eds-checkbox-box-height-responsive: calculateRem(24px);
$eds-checkbox-background-size-responsive: calculateRem(24px) calculateRem(24px);
$eds-checkbox-box-top-position: calculateRem(2px);
$eds-checkbox-box-border-radius: calculateRem(1px);
$eds-checkbox-box-border-width: calculateRem(1px);
$eds-checkbox-label-line-height: calculateRem(20px);

$eds-checkbox-tick-width: calculateRem(6px);
$eds-checkbox-tick-height: calculateRem(11px);
$eds-checkbox-tick-rotation: calculateRem(45deg);
$eds-checkbox-tick-position-top:  calculateRem(2px);
$eds-checkbox-tick-position-left: calculateRem(0px);

$eds-checkbox-hover-border-width: calculateRem(1px);
$eds-checkbox-focus-border-width: calculateRem(1px);

$eds-checkbox-margin-bottom: calculateRem(12px);
$eds-checkbox-margin-bottom-responsive: calculateRem(28px);
