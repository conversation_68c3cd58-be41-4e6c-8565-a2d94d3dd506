import { render, screen, fireEvent } from '@testing-library/react';
import { NotificationCardV2 } from '../Component';
import { NotificationCardOwnProps, ServiceNotification, NotificationObjectType } from '../models';

// Mock dependencies
jest.mock('react-tooltip', () => {
  return function MockReactTooltip(props: any) {
    return <div data-testid="react-tooltip" {...props} />;
  };
});

jest.mock('cx-dle-common-lib', () => ({
  useLocaleMoment: jest.fn(() => ({
    humanizeFromNow: jest.fn((timestamp: string) => {
      if (timestamp === '2023-01-01T10:00:00Z') return '2 hours ago';
      if (timestamp === '2023-01-01T08:00:00Z') return '4 hours ago';
      return '1 hour ago';
    }),
  })),
}));

jest.mock('cx-dle-component-library', () => ({
  CoverageExpiredSvg: () => <div data-testid="coverage-expired-svg" />,
  EDSIcon: ({ icon }: { icon: string }) => <div data-testid="eds-icon" data-icon={icon} />,
  ExpandableCardInfoItem: ({ children, className }: { children: any; className: string }) => (
    <div data-testid="expandable-card-info-item" className={className}>
      {children}
    </div>
  ),
  GeButton: ({ onClick, btnLabel, className, btnStyleType }: any) => (
    <button
      data-testid="ge-button"
      onClick={onClick}
      className={className}
      data-style-type={btnStyleType}
    >
      {btnLabel}
    </button>
  ),
  IconBadge: ({ children, type, size }: { children: any; type: string; size: string }) => (
    <div data-testid="icon-badge" data-type={type} data-size={size}>
      {children}
    </div>
  ),
  ModalityIcon: ({ code, className }: { code: string; className: string }) => (
    <div data-testid="modality-icon" data-code={code} className={className} />
  ),
  TruncateText: ({ text, maxLength }: { text: string; maxLength: number }) => (
    <span data-testid="truncate-text" data-max-length={maxLength}>
      {text}
    </span>
  ),
}));

jest.mock('../../../../../context/LanguageContext', () => ({
  useLanguage: jest.fn(() => ({
    language: 'en-us',
  })),
}));

jest.mock('../../../../../utils/addressUtils', () => ({
  formatEquipmentLocationForTooltipV2: jest.fn((eventData) => 
    `${eventData?.physicalLocationName || ''}, ${eventData?.physicalLocationCity || ''} ${eventData?.physicalLocationRegion || ''}`
  ),
}));

jest.mock('../../../../Common/ServiceStateIcon', () => {
  return function MockServiceStateIcon(props: any) {
    return <div data-testid="service-state-icon" {...props} />;
  };
});

// Mock console.log to avoid noise in tests
const consoleSpy = jest.spyOn(console, 'log').mockImplementation(() => {});

describe('NotificationCardV2 Component', () => {
  const mockSelectButtonHandler = jest.fn();

  const baseServiceNotification: ServiceNotification = {
    assetRowId: 'asset-123',
    read: false,
    subject: 'Test Subject',
    orderTimestamp: '2023-01-01T10:00:00Z',
    notificationId: 'notification-123',
    user: {
      id: 'user-123',
      firstname: 'John',
      lastname: 'Doe',
      email: '<EMAIL>',
      locale: 'en-us',
    },
    eventData: {
      eventId: 'event-123',
      eventTimestamp: '2023-01-01T08:00:00Z',
      physicalLocationName: 'Main Hospital',
      physicalLocationCity: 'New York',
      physicalLocationRegion: 'NY',
    },
    creationTimestamp: '2023-01-01T10:00:00Z',
    objectRowId: 'object-123',
    objectType: NotificationObjectType.ServiceRequest,
    modality: {
      id: 'CT',
      abbreviation: 'CT',
      description: 'Computed Tomography',
      preferred: true,
      isActive: true,
      modalityDisplayName: 'CT Scanner',
    },
    productDescription: 'CT Scanner Model XYZ',
    equipmentId: 'EQ-12345',
    engineerTypeCode: 'ENG01',
    serviceStateMessageCode: 'MSG01',
    serviceStateGeneratedMessage: 'Service in progress',
    sfdcNotificationFlag: {
      code: 'FLAG01',
      title: 'Service Request',
      icon: {
        iconName: 'service-icon',
        iconPrefix: 'ico',
        iconBackground: 'primary',
      },
    },
    requester: {
      fullName: 'Jane Smith',
    },
    contractExpirationDate: '2024-12-31',
    warrantyExpirationDate: '2024-06-30',
    endOfServiceLife: '2025-12-31',
  };

  const baseProps: NotificationCardOwnProps = {
    buttonText: 'View Details',
    warrantyExpiredText: 'Warranty Expired',
    onWatchMessageText: 'On Watch',
    contractExpiredText: 'Contract Expired',
    overduePMText: 'Overdue PM',
    onHoldMessageText: 'On Hold',
    item: baseServiceNotification,
    selectButtonHandler: mockSelectButtonHandler,
    dateFormat: 'MM/DD/YYYY',
  };

  beforeEach(() => {
    jest.clearAllMocks();
    consoleSpy.mockClear();
  });

  afterAll(() => {
    consoleSpy.mockRestore();
  });

  describe('Basic Rendering', () => {
    it('renders without crashing', () => {
      render(<NotificationCardV2 {...baseProps} />);
      expect(screen.getByText('Service Request')).toBeInTheDocument();
    });

    it('renders notification title from sfdcNotificationFlag', () => {
      render(<NotificationCardV2 {...baseProps} />);
      expect(screen.getByText('Service Request')).toBeInTheDocument();
    });

    it('renders creation timestamp', () => {
      render(<NotificationCardV2 {...baseProps} />);
      expect(screen.getByText('2 hours ago')).toBeInTheDocument();
    });

    it('renders equipment ID', () => {
      render(<NotificationCardV2 {...baseProps} />);
      expect(screen.getByText('EQ-12345')).toBeInTheDocument();
    });

    it('renders product description', () => {
      render(<NotificationCardV2 {...baseProps} />);
      expect(screen.getByText('CT Scanner Model XYZ')).toBeInTheDocument();
    });

    it('renders location information', () => {
      render(<NotificationCardV2 {...baseProps} />);
      expect(screen.getByText('Main Hospital, New York NY')).toBeInTheDocument();
    });
  });

  describe('Service Request Type', () => {
    it('renders action button for service request type', () => {
      const serviceRequestProps = {
        ...baseProps,
        item: {
          ...baseServiceNotification,
          objectType: NotificationObjectType.ServiceRequest,
        },
      };

      render(<NotificationCardV2 {...serviceRequestProps} />);
      
      const button = screen.getByTestId('ge-button');
      expect(button).toBeInTheDocument();
      expect(button).toHaveTextContent('View Details');
    });

    it('calls selectButtonHandler when action button is clicked', () => {
      const serviceRequestProps = {
        ...baseProps,
        item: {
          ...baseServiceNotification,
          objectType: NotificationObjectType.ServiceRequest,
        },
      };

      render(<NotificationCardV2 {...serviceRequestProps} />);
      
      const button = screen.getByTestId('ge-button');
      fireEvent.click(button);
      
      expect(mockSelectButtonHandler).toHaveBeenCalledTimes(1);
    });

    it('renders ServiceStateIcon for service request type', () => {
      const serviceRequestProps = {
        ...baseProps,
        item: {
          ...baseServiceNotification,
          objectType: NotificationObjectType.ServiceRequest,
        },
      };

      render(<NotificationCardV2 {...serviceRequestProps} />);
      expect(screen.getByTestId('service-state-icon')).toBeInTheDocument();
    });
  });

  describe('Asset Type', () => {
    it('does not render action button for asset type', () => {
      const assetProps = {
        ...baseProps,
        item: {
          ...baseServiceNotification,
          objectType: NotificationObjectType.Asset,
        },
      };

      render(<NotificationCardV2 {...assetProps} />);
      expect(screen.queryByTestId('ge-button')).not.toBeInTheDocument();
    });

    it('renders CoverageExpiredSvg for asset type', () => {
      const assetProps = {
        ...baseProps,
        item: {
          ...baseServiceNotification,
          objectType: NotificationObjectType.Asset,
        },
      };

      render(<NotificationCardV2 {...assetProps} />);
      expect(screen.getByTestId('coverage-expired-svg')).toBeInTheDocument();
    });

    it('renders IconBadge with danger type for asset type', () => {
      const assetProps = {
        ...baseProps,
        item: {
          ...baseServiceNotification,
          objectType: NotificationObjectType.Asset,
        },
      };

      render(<NotificationCardV2 {...assetProps} />);

      const iconBadge = screen.getByTestId('icon-badge');
      expect(iconBadge).toBeInTheDocument();
      expect(iconBadge).toHaveAttribute('data-type', 'danger');
      expect(iconBadge).toHaveAttribute('data-size', 'medium');
    });
  });

  describe('Modality Rendering', () => {
    it('renders modality icon when modality ID is present', () => {
      render(<NotificationCardV2 {...baseProps} />);

      const modalityIcon = screen.getByTestId('modality-icon');
      expect(modalityIcon).toBeInTheDocument();
      expect(modalityIcon).toHaveAttribute('data-code', 'CT');
      expect(modalityIcon).toHaveClass('notification-cardV2-modality-icon');
    });

    it('renders modality abbreviation', () => {
      render(<NotificationCardV2 {...baseProps} />);

      const truncateTexts = screen.getAllByTestId('truncate-text');
      const modalityAbbreviation = truncateTexts.find(el =>
        el.textContent === 'CT' && el.getAttribute('data-max-length') === '5'
      );
      expect(modalityAbbreviation).toBeInTheDocument();
    });

    it('does not render modality icon when modality ID is missing', () => {
      const propsWithoutModality = {
        ...baseProps,
        item: {
          ...baseServiceNotification,
          modality: {
            ...baseServiceNotification.modality!,
            id: '',
          },
        },
      };

      render(<NotificationCardV2 {...propsWithoutModality} />);
      expect(screen.queryByTestId('modality-icon')).not.toBeInTheDocument();
    });
  });

  describe('Location and Tooltip', () => {
    it('renders location icon', () => {
      render(<NotificationCardV2 {...baseProps} />);

      const locationIcon = screen.getByTestId('eds-icon');
      expect(locationIcon).toHaveAttribute('data-icon', 'ico-location-16');
    });

    it('renders tooltip with correct ID', () => {
      render(<NotificationCardV2 {...baseProps} />);

      const tooltip = screen.getByTestId('react-tooltip');
      expect(tooltip).toHaveAttribute('id', 'event-123');
    });

  });

  describe('Edge Cases and Error Handling', () => {
    it('handles missing sfdcNotificationFlag gracefully', () => {
      const propsWithoutFlag = {
        ...baseProps,
        item: {
          ...baseServiceNotification,
          sfdcNotificationFlag: undefined,
        },
      };

      render(<NotificationCardV2 {...propsWithoutFlag} />);
      expect(screen.queryByText('Service Request')).not.toBeInTheDocument();
    });

    it('handles missing notificationId', () => {
      const propsWithoutNotificationId = {
        ...baseProps,
        item: {
          ...baseServiceNotification,
          notificationId: '',
        },
      };

      render(<NotificationCardV2 {...propsWithoutNotificationId} />);
      expect(screen.queryByTestId('service-state-icon')).not.toBeInTheDocument();
      expect(screen.queryByTestId('coverage-expired-svg')).not.toBeInTheDocument();
    });

    it('handles missing product description', () => {
      const propsWithoutDescription = {
        ...baseProps,
        item: {
          ...baseServiceNotification,
          productDescription: undefined,
        },
      };

      render(<NotificationCardV2 {...propsWithoutDescription} />);

      const truncateTexts = screen.getAllByTestId('truncate-text');
      const productDescription = truncateTexts.find(el =>
        el.getAttribute('data-max-length') === '200'
      );
      expect(productDescription).toHaveTextContent('');
    });

    it('handles missing equipment ID', () => {
      const propsWithoutEquipmentId = {
        ...baseProps,
        item: {
          ...baseServiceNotification,
          equipmentId: undefined,
        },
      };

      render(<NotificationCardV2 {...propsWithoutEquipmentId} />);
      expect(screen.queryByText('EQ-12345')).not.toBeInTheDocument();
    });

    it('logs timestamps to console', () => {
      render(<NotificationCardV2 {...baseProps} />);

      expect(consoleSpy).toHaveBeenCalledWith('Creation Timestamp:', '2 hours ago');
      expect(consoleSpy).toHaveBeenCalledWith('Event Timestamp:', '4 hours ago');
    });
  });

  describe('Active/Inactive State', () => {

    it('determines inactive state when sfdcNotificationFlag is missing', () => {
      const inactiveProps = {
        ...baseProps,
        item: {
          ...baseServiceNotification,
          sfdcNotificationFlag: undefined,
        },
      };

      render(<NotificationCardV2 {...inactiveProps} />);

      // Component should still render but without the flag-dependent elements
      expect(screen.getByText('EQ-12345')).toBeInTheDocument();
    });
  });

  describe('Component Structure', () => {
    it('renders main container with correct class', () => {
      const { container } = render(<NotificationCardV2 {...baseProps} />);
      expect(container.querySelector('.notification-cardV2')).toBeInTheDocument();
    });

    it('renders flex box container', () => {
      const { container } = render(<NotificationCardV2 {...baseProps} />);
      expect(container.querySelector('.notification-cardV2__flex-box')).toBeInTheDocument();
    });

    it('renders type section', () => {
      const { container } = render(<NotificationCardV2 {...baseProps} />);
      expect(container.querySelector('.notification-cardV2__type')).toBeInTheDocument();
    });

    it('renders info section', () => {
      const { container } = render(<NotificationCardV2 {...baseProps} />);
      expect(container.querySelector('.notification-cardV2__info')).toBeInTheDocument();
    });
  });
});
