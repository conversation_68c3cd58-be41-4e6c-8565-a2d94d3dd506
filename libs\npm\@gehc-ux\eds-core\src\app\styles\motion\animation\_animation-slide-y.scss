/**
*  _slide-animation.scss
*
*
*
*/

@import '../common/variables';

/**
* SLIDE ANIMATION (4 Types, up, down, left, right, each with an easing modifier)
* SLIDE animation uses a map to generate slide levels and is configured to use easings to indicate entering/exiting.
* The slide animation will animate opacity and transform properties. Does not adjust display property. 
**/

/* 
* SLIDE Y 
* Slides up and down
*/

    /* 
    * SLIDE IN - Uses the enter easing
    */

    @include block('#{$animate}') {
      @include element('slide-in') {
        @each $level, $slide-y in $slide-y-map {
            @include modifier('#{$level}') {
              animation-name: slide-in-y-#{$level}; 
              animation-fill-mode: forwards;
              animation-timing-function: $enter-curve;
            }
        }
      }
    }

    @each $level, $slide-y in $slide-y-map {
      @keyframes slide-in-y-#{$level} {
            0% {
              opacity: 0;
              transform: translateY(0);
            }
            100% {
              opacity: 1;
              transform: translateY(#{$slide-y});
            }
        }
    }

    /* 
    * SLIDE OUT - Uses the exit easing
    */

    @include block('#{$animate}') {
      @include element('slide-out') {
        @each $level, $slide-y in $slide-y-map {
            @include modifier('#{$level}') {
              animation-name: slide-out-y-#{$level}; 
              animation-fill-mode: forwards;
              animation-timing-function: $enter-curve;
            }
        }
      }
    }

    @each $level, $slide-y in $slide-y-map {
      @keyframes slide-out-y-#{$level} {
            0% {
              opacity: 1;
              transform: translateY(0);
            }
            100% {
              opacity: 0;
              transform: translateY(#{$slide-y});
            }
        }
    }