/**
 *  eds-tracking.spec.ts
 *
 *  Copyright (c) 2018 by General Electric Company. All rights reserved.
 *
 *  The copyright to the computer software herein is the property of
 *  General Electric Company. The software may be used and/or copied only
 *  with the written permission of General Electric Company or in accordance
 *  with the terms and conditions stipulated in the agreement/contract
 *  under which the software has been supplied.
 *
 * Created by ********* on 2018-09-20.
 */

const fetchMock = require('fetch-mock');

const routeBase = '/v1/components';
import { EDSTracking } from './eds-tracking';
import { GlobalTracking } from './tracking-global';
import { nextTick } from 'q';

class TrackingConsumer extends EDSTracking {
  constructor(componentName: string) {
    super(componentName);
  }
  trigger(param?: any) {
    return this.track(param);
  }

  /**
   * since this TrackingConsumer class is not really an angular component,
   * we want to be able to trigger the angular lifecycle methods since
   * we have tracking workg being done in each of these. this allows us
   * to assert that the work inside these implmentations was done.
   * @param callback allow for asynchronous handling after ng lifecycle
   * methods were invoked.
   *
   */
  triggerNgLifeCycle(callback) {
    this.ngOnInit();
    setTimeout(() => {
      this.ngAfterViewInit();
      callback();
    }, 1);
  }

  getTrackingPreferences() {
    return this.getTrackingPrefs();
  }
}

const getGEHCNamespace = (): any => {
  const win = <any>window;
  win.GEHC = win.GEHC || {};
  return win.GEHC;
};

const getEDSNamespace = (): any => {
  getGEHCNamespace().EDS = getGEHCNamespace().EDS || {};
  return getGEHCNamespace().EDS;
};

const getComponentsMeta = (): any => {
  getEDSNamespace().components = getEDSNamespace().components || {};
  return getEDSNamespace().components;
};

const clearEDSNamespace = () => {
  delete getEDSNamespace().components;
  delete getEDSNamespace().trackingPrefs;
};

/** setup for a component... allows test to set up properties on
 *  global namespace
 */
const setupNamespaceForComponent = (componentName, componentMeta) => {
  getComponentsMeta()[componentName] = componentMeta;
};

const defaultStaticConfig = {
  trackUsage: true,
  disableNetworkPost: false,
  server: '',
  browserDebug: false,
  applicationName: 'Life Saver',
  organization: 'Acme Medical',
  trackPerformance: false
};

const getTrackingConfig = () => {
  return JSON.parse(JSON.stringify(defaultStaticConfig));
};

describe('EDS tracking implementation', () => {
  describe('configuration', () => {
    beforeEach(() => {
      GlobalTracking.initGlobal();
      window.GEHC.EDS.trackingPrefs = defaultStaticConfig;
      spyOn(window, 'fetch');
    });
    afterEach(clearEDSNamespace);
    describe('when user opts out from build config', () => {
      it('should not track', done => {

        window.GEHC.EDS.trackingPrefs.trackUsage = false;
        const edsTrackee = new TrackingConsumer('eds-awesome');
        edsTrackee.trigger({ name: 'eds-awesome' });

        setTimeout(() => {
          expect(window.fetch).not.toHaveBeenCalled();
          done();
        }, window.GEHC.EDS.tracking.debounceTiming + 10);
      });
    });
    describe('when user opts in', () => {
      it('should track', done => {
        const edsTrackee = new TrackingConsumer('eds-awesome');

        window.GEHC.EDS.trackingPrefs.trackUsage = true;
        edsTrackee.trigger({ name: 'eds-awesome' });

        setTimeout(() => {
          expect(window.fetch).toHaveBeenCalled();
          done();
        }, window.GEHC.EDS.tracking.debounceTiming + 10);
      });
    });
    describe('when browserDebug is enabled', () => {
      beforeEach(() => {
        spyOn(console, 'log');
      });
      it('should log tracking to browser `console.log`', done => {
        window.GEHC.EDS.trackingPrefs.browserDebug = true;
        const edsTrackee = new TrackingConsumer('eds-awesome');
        edsTrackee.trigger({ name: 'eds-awesome' });
        setTimeout(() => {
          expect(console.log).toHaveBeenCalled();
          done();
        }, window.GEHC.EDS.tracking.debounceTiming + 10);
      });
    });
  });

  describe('incomplete data', () => {
    afterEach(clearEDSNamespace);

    beforeEach(() => {
      GlobalTracking.initGlobal();
    });
    describe('when caller of `track` function does not provide `componentData`', () => {
      it('should throw an error', () => {
        const edsTrackee = new TrackingConsumer('eds-awesome');
        expect(() => {
          edsTrackee.trigger();
        }).toThrow();
      });
    });
    describe('when caller of `track` function does not provide `componentData.componentName`', () => {
      it('should throw an error', () => {
        const edsTrackee = new TrackingConsumer('eds-awesome');
        expect(() => {
          edsTrackee.trigger({});
        }).toThrow();
      });
    });
  });

  describe('verifying composed post data', () => {
    afterEach(() => {
      fetchMock.reset();
      clearEDSNamespace();
    });

    beforeEach(() => {

      window.GEHC = window.GEHC || {};
      window.GEHC.EDS = window.GEHC.EDS || {};
      window.GEHC.EDS.trackingPrefs = getTrackingConfig();

      GlobalTracking.initGlobal();
    });
    it('should post composed data from config and component meta', done => {
      const componentName = 'eds-awesome';
      const trackingConfig = getTrackingConfig();
      const edsTrackee = new TrackingConsumer(componentName);
      setupNamespaceForComponent(componentName, { version: '0.0.1' });
      const url = routeBase;
      const result = fetchMock.mock(url, 200);

      edsTrackee.triggerNgLifeCycle(() => {

        setTimeout(() => {

          const postedBody = JSON.parse(result.lastOptions().body);
          expect(postedBody.organization).toEqual(trackingConfig.organization);
          expect(postedBody.applicationName).toEqual(
            trackingConfig.applicationName
          );
          expect(postedBody.components[componentName].version).toEqual(
            getEDSNamespace().components[componentName].version
          );
          done();
        }, window.GEHC.EDS.tracking.debounceTiming + 10);
      });

    });
  });

  describe('when performance tracking is NOT enabled', () => {
    afterEach(() => {
      fetchMock.reset();
      clearEDSNamespace();
    });
    beforeEach(() => {
      window.GEHC = window.GEHC || {};
      window.GEHC.EDS = window.GEHC.EDS || {};
      window.GEHC.EDS.trackingPrefs = getTrackingConfig();

      GlobalTracking.initGlobal();
    });
    it('should NOT post `loadTime` for each component on initialization', done => {
      const componentName = 'foo';
      setupNamespaceForComponent(componentName, { version: '0.0.1' });
      const url = routeBase;
      const result = fetchMock.mock(url, 200);
      const edsTrackee = new TrackingConsumer(componentName);

      edsTrackee.triggerNgLifeCycle(() => {
        setTimeout(() => {

          const postedBody = JSON.parse(result.lastOptions().body);
          expect(postedBody.components[componentName].loadTime.length).toBe(0);
          done();
        }, window.GEHC.EDS.tracking.debounceTiming + 10);
      });
    });
  });

  describe('when performance tracking is enabled', () => {
    afterEach(() => {
      fetchMock.reset();
      clearEDSNamespace();
    });
    beforeEach(() => {
      window.GEHC = window.GEHC || {};
      window.GEHC.EDS = window.GEHC.EDS || {};
      window.GEHC.EDS.trackingPrefs = getTrackingConfig();

      GlobalTracking.initGlobal();
    });
    it('should post `loadTime` for each component on initialization', done => {
      const componentName = 'eds-awesome';
      window.GEHC.EDS.trackingPrefs.trackPerformance = true;
      setupNamespaceForComponent(componentName, { version: '0.0.1' });
      const url = routeBase;
      const result = fetchMock.mock(url, 200);
      const edsTrackee = new TrackingConsumer(componentName);

      edsTrackee.triggerNgLifeCycle(() => {
        setTimeout(() => {

          const postedBody = JSON.parse(result.lastOptions().body);
          expect(postedBody.components[componentName].loadTime[0]).toBeGreaterThan(0);
          done();
        }, window.GEHC.EDS.tracking.debounceTiming + 10);
      });
    });
  });

  describe('custom config override', () => {
    describe('if user programattically disables tracking', () => {
      beforeEach(() => {
        GlobalTracking.initGlobal();
        spyOn(window, 'fetch');
      });
      afterEach(clearEDSNamespace);
      it('should not post any data', done => {
        const componentName = 'eds-awesome';
        const edsTrackee = new TrackingConsumer(componentName);
        // user runtime-override
        window.GEHC.EDS.trackingPrefs.trackUsage = false;
        setupNamespaceForComponent(componentName, { version: '0.0.1' });
        edsTrackee.trigger({ name: componentName });
        setTimeout(() => {
          expect(window.fetch).not.toHaveBeenCalled();
          done();
        }, window.GEHC.EDS.tracking.debounceTiming + 10);
      });
    });
  });
});
