/**
*  _fade-animation.scss
*
*
*
*/

@import '../common/variables';

/**
* FADE ANIMATION
* FADE animation adds an animation to the class it is applied to.
* The fade animation will animate opacity from 0 to 1. Does not adjust display property. 
**/

    /* 
    * FADE IN - Uses the enter easing
    */

    @include block('#{$animate}') {
      @include element('fade-in') {
          animation-name: fade-in; 
          animation-fill-mode: forwards;
          animation-timing-function: $enter-curve;
      }
    }

    @keyframes fade-in{
        0% {
          opacity: 0;
        }
        100% {
          opacity: 1;
        }
    }


    /* 
    * FADE OUT - Uses the exit easing
    */ 

    @include block('#{$animate}') {
      @include element('fade-out') {
          animation-name: fade-out; 
          animation-fill-mode: forwards;
          animation-timing-function: $exit-curve;
      }
    }

    @keyframes fade-out{
        0% {
          opacity: 1;
        }
        100% {
          opacity: 0;
        }
    }









