/**
 * @main
 * @name Badge
 * @usage
 * Badges are generally used for emphasizing a numerical value of an object,
 * e.g., the number of unread items in a folder. Other uses may include
 * communicating status and other types of non-critical notifications in the
 * application interface.
 *
 * @class badge--info-1 Modifier to create an information badge
 * @class badge--info-1-alt Modifier to create an information badge
 * @class badge--info-2 Modifier to create an information badge
 * @class badge--info-2-alt Modifier to create an information badge
 * @class badge--info-3 Modifier to create an information badge
 * @class badge--info-3-alt Modifier to create an information badge
 * @class badge--info-4 Modifier to create an information badge
 * @class badge--info-4-alt Modifier to create an information badge
 * @class badge--info-5 Modifier to create an information badge
 * @class badge--info-5-alt Modifier to create an information badge
 * @class badge--info-6 Modifier to create an information badge
 * @class badge--info-6-alt Modifier to create an information badge
 * @class badge--severity-high Modifier to create an high severity badge
 * @class badge--severity-high-alt Modifier to create a high severity badge
 * @class badge--severity-low Modifier to create a low severity badge
 * @class badge--severity-low-alt Modifier to create a low severity badge
 * @class badge--severity-moderate Modifier to create a moderate severity badge
 * @class badge--severity-moderate-alt Modifier to create a moderate severity badge
 * @class badge--success Modifier to create a success badge
 *
 * #### Informational Badge Markup Example
 * ```html
 * <span class="badge text-14">Badge Label</span>
 * <span class="badge badge--small text-14">Badge: Small</span>
 * <span class="badge badge--large text-14">Badge: Large</span>
 * <span class="badge badge--flag text-14">Badge: Flag Style</span>
 * <span class="badge badge--error text-14">Badge: Error</span>
 * <span class="badge badge--warning text-14">Badge: Warning</span>
 * <span class="badge badge--healthy text-14">Badge: Healthy</span>
 * <span class="badge badge--important text-14">Badge: Important</span>
 * <span class="badge badge--info-1 text-14">Badge: Information</span>
 * ```
 *
 * #### Notification Badge Markup Example
 * ```html
 * <span class="badge badge--notification" aria-label="Badge Label">
 *   <span class="badge__count text-14-bold">3</span>
 *   <span class="badge__icon">[icon]</span>
 * </span>```
 *
 *
 * #### Notification Text Badge Markup Example
 * ```html
 * <span class="badge badge--notification">
 *   <span class="badge__count text-14-bold u-m0">3</span>
 *   <span class="text-bold-16">{{badgeLabel}}</span>
 * </span>```
 *
 * @browsers chrome, safari, firefox, ie11, edge
 * @description
 * Small numerical value or status descriptor for UI elements.
 *
 */

@import 'node_modules/@gehc-ux/eds-core/src/app/styles/component_base';
@import 'node_modules/@gehc-ux/eds-typography/scss/typography';
@import './variables';

@mixin eds-badge {

  /**
   * @class badge Main class carrying the badge styles
   */
  @include block('badge') {
    align-items: center;
    background-color: var(--eds-badge-background-color);
    border-radius: $eds-badge-border-radius;
    color: var(--eds-badge-text-color);
    display: inline-flex;
    -webkit-font-smoothing: antialiased;
    height: $eds-badge-height-medium;
    padding: $eds-badge-padding-none $eds-badge-padding;
    position: relative;
    z-index: 1;

    eds-icon,
    svg {
      display: inline-block;
      fill: currentColor;
      margin-right: $eds-badge-icon-margin-right;
      margin-left: $eds-badge-icon-margin-left;
    }

    /**
     * @class badge__count Wrapping class for notification counts
     */
    @include element('count') {
      align-items: center;
      background-color: var(--eds-badge-background-color-count);
      border: $eds-badge-count-border-width solid var(--eds-badge-border-color-count);
      border-radius: $eds-badge-notification-count-radius;
      bottom: 100%;
      box-sizing: border-box;
      color: var(--eds-badge-text-color-count);
      display: flex;
      font-size: $eds-badge-count-font-size;
      font-weight: bold;
      height: $eds-badge-count-size;
      justify-content: center;
      left: 100%;
      letter-spacing: $eds-badge-letter-spacing;
      margin-left: -50%;
      min-width: $eds-badge-count-size;
      overflow: hidden;
      padding: 0 $eds-badge-notification-count-padding;
      position: absolute;
      text-align: center;
      transform: translateY(calc($eds-badge-count-size / 2));
      z-index: 1;

      /**
       * @class badge__count--large Larger-sized standalone counter
       */
      @include modifier('large') {
        font-size: $eds-badge-count-font-size-large;
        line-height: $eds-badge-count-line-height-large;
        height: $eds-badge-count-size-large;
        min-width: $eds-badge-count-size-large;
        position: static;
        transform: none;
      }

      // If there's another path in the svg, the color will need to be inverted
      svg {
        height: $eds-badge-notification-icon-size;;
        width: $eds-badge-notification-icon-size;

        path:nth-of-type(2) {
          fill: var(--eds-badge-text-color);
        }
      }

      .button--icon[disabled] & {
        background-color: var(--eds-badge-icon-color-disabled);
      }

    }

    /**
     * @class badge--notification Modifier to create a notification badge
     */
    @include modifier('notification') {
      background: transparent;
      border-radius: $eds-badge-notification-border-radius;
      padding: $eds-badge-notification-padding;
      position: relative;

      eds-icon,
      svg {
        margin: $eds-badge-notification-icon-margin;
      }
    }

    /**
     * @class badge__icon Wrapping class for notification icons
     */
    @include element('icon') {
      color: var(--eds-badge-icon-color);
      position: relative;

      // Styles for when this is nested inside a button
      .button--icon:hover &,
      .button--icon:focus & {
        color: var(--eds-badge-icon-color-hover);
      }

      .button--icon:active &,
      .button--icon[aria-pressed="true"] &,
      .button--icon[aria-expanded="true"] & {
        color: var(--eds-badge-icon-color-pressed);
      }

      .button--icon[disabled] & {
        color: var(--eds-badge-icon-color-disabled);
      }

    }

    /**
     * @class badge__text Wrapping class for notification text
     */
    @include element('text') {
      color: var(--eds-badge-icon-color);
    }

    /**
     * @class badge--small Modifier to create a small badge
     */
    @include modifier('small') {
        height: $eds-badge-height-small;
        padding: $eds-badge-notification-padding $eds-badge-padding-small;

        eds-icon,
        svg {
          margin-left: $eds-badge-icon-margin-left-small;
        }

        @include element('count') {
            height: $eds-badge-count-size-small;
            min-width: $eds-badge-count-min-width;
            overflow: hidden;
            text-indent: -999em; // just a big number to move the text off screen
            transform: translateY(calc($eds-badge-count-size-small / 1.4));
            width: $eds-badge-count-size-small;

            // If there's another path in the svg, the color will need to be inverted
            svg {
              display: none;
            }

        }
    }

    /**
     * @class badge--large Modifier to create a large badge
     */
    @include modifier('large') {
      height: $eds-badge-height-large;

      eds-icon,
      svg {
        margin-left: $eds-badge-icon-margin-left-large;
      }

    }

    /**
     * @class badge--flag Modifier to create a flag badge
     */
    @include modifier('flag') {
      border-radius: $eds-badge-flag-border-radius;
      padding-left: $eds-badge-padding;
      padding-right: $eds-badge-flag-padding-right;
      margin-right: $eds-badge-flag-width;

      &.badge--small {
        svg,
        eds-icon {
          margin-left: $eds-badge-icon-margin-left-small-flag;
        }

      }

      @include after() {
        background-color: var(--eds-badge-background-color);
        content: "";
        height: 100%;
        position: absolute;
        transform: skewX(-20deg);
        right: -10px;
        top: 0;
        width: 100%;
        z-index: -1;
      }
    }

  }

  // Looping through all badge options to produce the classes we need
  @each $eds-badge-option-dark in $eds-badge-options-dark-text {
    .badge--#{$eds-badge-option-dark} {
      background-color: var(--eds-badge-background-color-#{$eds-badge-option-dark});
      color: var(--eds-badge-text-color);

      &.badge--flag:after {
        background-color: var(--eds-badge-background-color-#{$eds-badge-option-dark});
      }

      // If there's another path in the svg, the color will need to be inverted
      svg path:nth-of-type(2) {
        fill: var(--eds-badge-text-color-light);
      }

    }
  } // @each

  // Looping through all badge options to produce the classes we need
  @each $eds-badge-option in $eds-badge-options-light-text {
    .badge--#{$eds-badge-option} {
      background-color: var(--eds-badge-background-color-#{$eds-badge-option});
      color: var(--eds-badge-text-color-light);

      &.badge--flag:after {
        background-color: var(--eds-badge-background-color-#{$eds-badge-option});
      }

      // If there's another path in the svg, the color will need to be inverted
      svg path:nth-of-type(2) {
        fill: var(--eds-badge-text-color);
      }

    }
  } // @each

} // eds-badge();

@include eds-badge();
