import { render, screen, fireEvent } from '@testing-library/react';
import { IntroduceYourself } from '../Component';
import { IntroduceyourselfProps } from '../models';

// Mock the external dependencies
jest.mock('cx-dle-common-lib', () => ({
  FormConnector: ({ children }: { children: any }) => {
    const mockSetValue = jest.fn();
    return children({ SetValue: mockSetValue });
  },
  FormElement: ({ children, name }: { children: any; name: string; defaultValue?: any }) =>
    children({
      handleChange: jest.fn(),
      handleBlur: jest.fn(),
      handleFocus: jest.fn(),
      touched: { [name]: false },
      valid: { [name]: true }
    }),
  validationSchema: () => ({
    required: jest.fn().mockReturnThis(),
    customRule: jest.fn().mockReturnThis(),
  }),
}));

jest.mock('cx-dle-component-library', () => ({
  EDSIcon: ({ icon, className }: { icon: string; className: string }) => (
    <div data-testid="eds-icon" data-icon={icon} className={className} />
  ),
  FormMessage: ({ error, message }: { error: boolean; message: string }) => (
    error ? <div data-testid="form-message-error">{message}</div> : null
  ),
  FormSection: ({ children, className, titleText }: { children: any; className: string; titleText: string }) => (
    <section data-testid="form-section" className={className}>
      <h2>{titleText}</h2>
      {children}
    </section>
  ),
  GridCell: ({ children, desktop, tablet }: { children: any; desktop?: number; tablet?: number }) => (
    <div data-testid="grid-cell" data-desktop={desktop} data-tablet={tablet}>
      {children}
    </div>
  ),
  GridRow: ({ children }: { children: any }) => (
    <div data-testid="grid-row">{children}</div>
  ),
}));

jest.mock('cx-dle-component-library/components/PhoneNumberWithCountryCodeV1', () => ({
  PhoneNumberWithCountryCodeV1: ({
    defaultCountrySelected,
    dropdownLabel,
    defaultPhoneNumber,
    handleChange,
    handlePhoneNumberError,
    className,
    countryCodeDropDownList
  }: any) => (
    <div
      data-testid="phone-number-with-country-code"
      data-default-country={defaultCountrySelected}
      data-label={dropdownLabel}
      data-default-phone={defaultPhoneNumber}
      data-country-list-length={countryCodeDropDownList?.length || 0}
      className={className}
    >
      <input
        data-testid="phone-input"
        onChange={(e) => handleChange && handleChange(e.target.value)}
        onBlur={() => handlePhoneNumberError && handlePhoneNumberError(false)}
      />
    </div>
  ),
}));

jest.mock('../../../utils/commonUtils', () => ({
  renderFormFieldsByLocale: (fields: any[]) => fields,
}));

jest.mock('../../Common/GeTextInput', () => {
  return function MockGeTextInput({ 
    labelText, 
    name, 
    defaultValue, 
    placeHolder, 
    required, 
    className,
    inputMode 
  }: any) {
    return (
      <div data-testid={`ge-text-input-${name}`} className={className}>
        <label>{labelText}</label>
        <input 
          name={name}
          defaultValue={defaultValue}
          placeholder={placeHolder}
          required={required}
          inputMode={inputMode}
          data-testid={`input-${name}`}
        />
      </div>
    );
  };
});

jest.mock('html-react-parser', () => (html: string) => html);

// Mock window.location
Object.defineProperty(window, 'location', {
  value: {
    pathname: '/test-path',
  },
  writable: true,
});

const mockProps: IntroduceyourselfProps = {
  introduceYourselfCaption: 'Introduce Yourself',
  firstNameLabel: 'First Name',
  firstNameHelperTextLabel: 'Enter your first name',
  firstNameOptionalRequiredLabel: 'Required',
  firstNamePlaceholder: 'First Name',
  firstNameMaxCharacters: '50',
  firstNameOptional: false,
  firstNameRequiredValidationMessage: 'First name is required',
  firstNameRegEx: '[^a-zA-Z]',
  firstLocalNameLabel: 'Local First Name',
  lastNameMaxCharacters: '50',
  localFirstNameValidation: 'Local first name is required',
  firstNameLocalRegEx: '[^a-zA-Z]',
  lastNameLabel: 'Last Name',
  lastNameHelperTextLabel: 'Enter your last name',
  lastNameOptionalRequiredLabel: 'Required',
  lastNamePlaceholder: 'Last Name',
  lastNameOptional: false,
  lastNameRequiredValidationMessage: 'Last name is required',
  lastNameRegEx: '[^a-zA-Z]',
  lastLocalNameLabel: 'Local Last Name',
  localLastNameRequired: 'Required',
  localLastNameValidation: 'Local last name is required',
  lastNameLocalRegEx: '[^a-zA-Z]',
  countryCodeDropDown: [
    {
      name: 'United States',
      maxLength: '10',
      countryPhoneNumberPlaceholder: '(*************',
      flagSource: '/flags/us.png',
      validationMessage: 'Invalid US phone number',
      minLength: '10',
      countryCode: 'US',
      countryISDCode: '+1',
      countryName: 'United States',
      isAvailableInPackage: true,
    },
  ] as any,
  phoneNumberOptional: false,
  businessPhoneNumberValidationMessage: 'Business phone number is required',
  phoneRequiredValidationMessage: 'Phone number is required',
  phoneNumberOptionalRequiredLabel: 'Required',
  phoneNumberLabel: 'Phone Number',
  phoneNumberMaxCharacters: '15',
  phoneNumberPlaceholder: 'Phone Number',
  businessPhoneNumberLabel: 'Business Phone Number',
  phoneRegEx: '[^0-9]',
  phoneNumberHelperTextLabel: 'We will use this to contact you',
  customerAccountNumberLabel: 'Account Number',
  customerAccountNumberPlaceholder: 'Account Number',
  customerAccountNumberMaxCharacters: '20',
  accountNumberPopupTitle: 'Account Number Help',
  accountNumberPopupImage: {
    src: '/help-image.png',
    alt: 'Help Image',
    width: '300',
    height: '200',
  },
  accountNumberPopupText: 'Find your account number here',
  customerAccountNumberTextLabel: 'Account number help text',
  cnpjCPFLabel: 'CNPJ/CPF',
  cnpjCPFTextLabel: 'CNPJ/CPF help text',
  cnpjCPFOptionalRequiredLabel: 'Required',
  cnpjCPFPlaceholder: 'CNPJ/CPF',
  cnpjCPFMaxCharacters: '20',
  cnpjCPFValidationMessage: 'CNPJ/CPF is required',
  ultrasoundSerialLabel: 'Ultrasound Serial',
  ultrasoundSerialPlaceholder: 'Serial Number',
  ultrasoundSerialMaxCharacters: '20',
  ultrasoundSerialValidationMessage: 'Serial number is required',
  ultrasoundPopupTitle: 'Serial Number Help',
  ultrasoundPopupText: 'Find your serial number here',
  ultrasoundSerialHintText: 'Serial number help text',
  localFirstNameRequired: 'Required',
  sourceApplication: '',
  defaultValues: {
    FIRST_NAME: 'John',
    LAST_NAME: 'Doe',
    LOCAL_FIRST_NAME: '',
    LOCAL_LAST_NAME: '',
    PHONE: '+**********',
  },
  submitState: {},
  websiteCountryCode: 'US',
  globalCountry: 'US',
  handlePhoneNumberError: jest.fn(),
  preferredCountryCode: 'US',
};

describe('IntroduceYourself Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders without crashing', () => {
    render(<IntroduceYourself {...mockProps} />);
    
    expect(screen.getByTestId('form-section')).toBeInTheDocument();
    expect(screen.getByText('Introduce Yourself')).toBeInTheDocument();
  });

  it('renders first name input field', () => {
    render(<IntroduceYourself {...mockProps} />);
    
    expect(screen.getByTestId('ge-text-input-FIRST_NAME')).toBeInTheDocument();
    expect(screen.getByTestId('input-FIRST_NAME')).toHaveAttribute('defaultValue', 'John');
    expect(screen.getByTestId('input-FIRST_NAME')).toHaveAttribute('placeholder', 'First Name');
    expect(screen.getByText('First Name')).toBeInTheDocument();
  });

  it('renders last name input field', () => {
    render(<IntroduceYourself {...mockProps} />);
    
    expect(screen.getByTestId('ge-text-input-LAST_NAME')).toBeInTheDocument();
    expect(screen.getByTestId('input-LAST_NAME')).toHaveAttribute('defaultValue', 'Doe');
    expect(screen.getByTestId('input-LAST_NAME')).toHaveAttribute('placeholder', 'Last Name');
    expect(screen.getByText('Last Name')).toBeInTheDocument();
  });

  it('renders phone number field with country code dropdown when available', () => {
    render(<IntroduceYourself {...mockProps} />);
    
    expect(screen.getByTestId('phone-number-with-country-code')).toBeInTheDocument();
    expect(screen.getByTestId('phone-number-with-country-code')).toHaveAttribute('data-default-country', 'US');
    expect(screen.getByTestId('phone-number-with-country-code')).toHaveAttribute('data-default-phone', '+**********');
  });

  it('renders phone number as text input when no country code dropdown', () => {
    const propsWithoutDropdown = {
      ...mockProps,
      countryCodeDropDown: [],
    };
    
    render(<IntroduceYourself {...propsWithoutDropdown} />);
    
    expect(screen.getByTestId('ge-text-input-PHONE')).toBeInTheDocument();
    expect(screen.getByTestId('input-PHONE')).toHaveAttribute('inputMode', 'tel');
    expect(screen.getByTestId('input-PHONE')).toHaveAttribute('defaultValue', '+**********');
  });

  it('shows business phone label for service shop source', () => {
    const serviceShopProps = {
      ...mockProps,
      sourceApplication: 'gssserviceshop',
      countryCodeDropDown: [],
    };
    
    render(<IntroduceYourself {...serviceShopProps} />);
    
    expect(screen.getByText('Business Phone Number')).toBeInTheDocument();
  });

  it('renders phone number helper text', () => {
    render(<IntroduceYourself {...mockProps} />);
    
    expect(screen.getByText('We will use this to contact you')).toBeInTheDocument();
    expect(screen.getByTestId('eds-icon')).toHaveAttribute('data-icon', 'ico-info-32');
  });

  it('handles optional first name field', () => {
    const optionalProps = {
      ...mockProps,
      firstNameOptional: true,
    };
    
    render(<IntroduceYourself {...optionalProps} />);
    
    const firstNameInput = screen.getByTestId('input-FIRST_NAME');
    expect(firstNameInput).not.toHaveAttribute('required');
  });

  it('handles optional last name field', () => {
    const optionalProps = {
      ...mockProps,
      lastNameOptional: true,
    };
    
    render(<IntroduceYourself {...optionalProps} />);
    
    const lastNameInput = screen.getByTestId('input-LAST_NAME');
    expect(lastNameInput).not.toHaveAttribute('required');
  });

  it('handles optional phone number field', () => {
    const optionalProps = {
      ...mockProps,
      phoneNumberOptional: true,
      countryCodeDropDown: [],
    };
    
    render(<IntroduceYourself {...optionalProps} />);
    
    const phoneInput = screen.getByTestId('input-PHONE');
    expect(phoneInput).not.toHaveAttribute('required');
  });

  it('uses preferred country code for global signup page', () => {
    Object.defineProperty(window, 'location', {
      value: {
        pathname: '/global/signup',
      },
      writable: true,
    });

    const globalProps = {
      ...mockProps,
      preferredCountryCode: 'JP',
    };
    
    render(<IntroduceYourself {...globalProps} />);
    
    expect(screen.getByTestId('phone-number-with-country-code')).toHaveAttribute('data-default-country', 'JP');
  });

  it('handles phone number change correctly', () => {
    const handlePhoneNumberError = jest.fn();
    const propsWithHandler = {
      ...mockProps,
      handlePhoneNumberError,
    };
    
    render(<IntroduceYourself {...propsWithHandler} />);
    
    const phoneInput = screen.getByTestId('phone-input');
    fireEvent.change(phoneInput, { target: { value: '+**********' } });
    
    // The component should handle the change
    expect(phoneInput).toBeInTheDocument();
  });

  it('calls handlePhoneNumberError when phone error occurs', () => {
    const handlePhoneNumberError = jest.fn();
    const propsWithHandler = {
      ...mockProps,
      handlePhoneNumberError,
    };
    
    render(<IntroduceYourself {...propsWithHandler} />);
    
    const phoneInput = screen.getByTestId('phone-input');
    fireEvent.blur(phoneInput);
    
    expect(handlePhoneNumberError).toHaveBeenCalledWith(false);
  });

  it('renders with correct CSS classes', () => {
    render(<IntroduceYourself {...mockProps} />);
    
    expect(screen.getByTestId('form-section')).toHaveClass('introduce-yourself');
    expect(screen.getByTestId('ge-text-input-FIRST_NAME')).toHaveClass('text-required');
  });
});
