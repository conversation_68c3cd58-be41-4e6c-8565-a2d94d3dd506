/**
 * @main
 * @name Action Button
 * @usage
 * It is recommended to apply the button classes to a `<button>` element in order to benefit from
 * the native features of button, such as focus and accessibility. Content such as text or icon
 * can be added within the button.
 *
 * Adding the `button` class to an element will style it as a button. It can then be further decorated
 * in type (primary, secondary, tertiary) as well as being disabled.
 *
 * Types are primary (no class), secondary (`button--secondary`) or
 * tertiary (`button--tertiary`)
 *
 * The button can be disabled by adding a `disabled` attribute.
 *
 * The button can use an `eds-icon` within which will automatically get the appropriate colors through the `--eds-icon-color` css variable.
 * If you wish to use an svg rather than an `eds-icon`, you can add the .button__icon—color class which will ensure that the fill property will appropriately use the `--eds-icon-color`
 *
 * #### Button Types
 * ```html
 * <button class="button">Primary Button</button>
 * <button class="button button--secondary">Secondary Button</button>
 * <button class="button button--tertiary">Tertiary Button</button>
 * ```
 *
 * #### Disabled primary button
 * ```html
 * <button class="button" disabled="disabled">Primary Button</button>
 * ```
 *
 *  #### Primary Button with Icon
 * ```html
 * <button type="button" class="button">
 *  <svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"><title>ico-user</title><g id="User_Profile_-_16" data-name="User/Profile - 16"><path d="M16,12.5V16H0V12.5c0-1.66,1.63-3.1,4-3.88A5.28,5.28,0,0,0,8,10.5a5.28,5.28,0,0,0,4-1.88C14.37,9.4,16,10.84,16,12.5ZM8,0a4.27,4.27,0,0,1,4,4.5A4.27,4.27,0,0,1,8,9,4.27,4.27,0,0,1,4,4.5,4.27,4.27,0,0,1,8,0Z"/></g></svg>
 *  Button
 * </button>
 * ```
 *
 *  #### Icon Button
 * ```html
 * <button type="button" class="button button--icon" aria-hidden="Button text goes here">
 *  <svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"><title>ico-user</title><g id="User_Profile_-_16" data-name="User/Profile - 16"><path d="M16,12.5V16H0V12.5c0-1.66,1.63-3.1,4-3.88A5.28,5.28,0,0,0,8,10.5a5.28,5.28,0,0,0,4-1.88C14.37,9.4,16,10.84,16,12.5ZM8,0a4.27,4.27,0,0,1,4,4.5A4.27,4.27,0,0,1,8,9,4.27,4.27,0,0,1,4,4.5,4.27,4.27,0,0,1,8,0Z"/></g></svg>
 * </button>
 * ```
 * @browsers chrome, safari, firefox, ie11, edge
 * @description
 * The **action button** element is a visibly emphasized interactive component that clearly indicates to the user that it will perform an action or initiate a process when clicked or tapped.
 * @class button Base class to use for a button. Defaults to a medium secondary button.
 */


@import 'node_modules/@gehc-ux/eds-core/src/app/styles/component_base';
@import 'node_modules/@gehc-ux/eds-core/src/app/styles/global/animations';
@import 'node_modules/@gehc-ux/eds-core/src/app/styles/common/breakpoints';
@import './variables';
@import './mixins';

// This button mixin is imported into button groups as well
@mixin eds-basic-button {
  align-items: center;
  background: var( --eds-primary-button-background-color );
  border: none;
  border-radius: $eds-button-border-radius;
  color: var(--eds-primary-button-text-color);
  cursor: pointer;
  display: inline-flex;
  font-family: $eds-base-font-family;
  font-size: $eds-button-font-size;
  -webkit-font-smoothing: antialiased;
  font-weight: bold;
  height: $eds-button-height-small;
  line-height: $eds-button-line-height;
  padding: $eds-button-padding-small;
  text-align: center;
  transition: $transition;
  transition-property: color, box-shadow, background, border;
  white-space: nowrap;
  width: auto;

  @media (min-width: map-get($eds-breakpoints, "small")) {
    height: $eds-button-height;
    padding: $eds-button-padding;
  }

  @include hover {
    background: var(--eds-primary-button-hover-background-color);
    color: var(--eds-primary-button-text-color);
  }

  @include focus {
    background: var(--eds-primary-button-focus-background-color);
    color: var(--eds-primary-button-text-color);
  }

  &:active,
  &[aria-pressed="true"],
  &[aria-expanded="true"] {
    background: var(--eds-primary-button-active-background-color);
    color: var(--eds-primary-button-text-color);
  }

  @include disabled {
    background: var(--eds-primary-button-disabled-background-color);
    cursor: not-allowed;
    pointer-events: none;
    color: var(--eds-primary-button-disabled-text-color);
    --eds-icon-color: var(--eds-primary-button-disabled-text-color);
  }

} // @mixin eds-basic-button

@mixin eds-button {

  @include block('button') {

    @include eds-basic-button;

    /**
    * @class button--alt-primary Styles the button to be alternate primary
    */
    @include modifier('alt-primary') {
      background: var(--eds-alt-primary-button-background-color);
      color: var(--eds-alt-primary-button-text-color);
      --eds-icon-color: var(--eds-alt-primary-button-text-color);

      @include hover {
        background: var(--eds-alt-primary-button-hover-background-color);
        color: var(--eds-alt-primary-button-text-color);
      }

      @include focus {
        background: var(--eds-alt-primary-button-focus-background-color);
      }

      @include active {
        background: var(--eds-alt-primary-button-active-background-color);
      }

      @include disabled {
        background: var(--eds-alt-primary-button-disabled-background-color);
      }

    } // alt-primary

    /**
    * @class button--secondary Styles the button to be secondary
    */
    @include modifier('secondary') {
      background: var(--eds-secondary-button-background-color);
      color: var(--eds-secondary-button-text-color);
      --eds-icon-color: var(--eds-secondary-button-text-color);

      @include hover {
        background: var(--eds-secondary-button-hover-background-color);
        color: var(--eds-secondary-button-text-color);
      }

      @include focus {
        background: var(--eds-secondary-button-focus-background-color);
        color: var(--eds-secondary-button-text-color);
      }

      &:active,
      &[aria-pressed="true"],
      &[aria-expanded="true"] {
        background: var(--eds-secondary-button-active-background-color);
        color: var(--eds-secondary-button-text-color);
      }

      @include disabled {
        background: var(--eds-secondary-button-disabled-background-color);
      }

    } // secondary

    /**
    * @class button--tertiary Styles the button to be a tertiary button
    */
    @include modifier('tertiary') {
      background: var(--eds-tertiary-button-background-color);
      box-shadow: inset 0 0 0 1px var(--eds-tertiary-button-border-color);
      font-weight: normal;
      color: var(--eds-tertiary-button-text-color);
      --eds-icon-color: var(--eds-tertiary-button-text-color);

      @include hover {
        background: var(--eds-tertiary-button-hover-background-color);
        box-shadow: inset 0 0 0 1px var(--eds-tertiary-button-hover-border-color);
        color: var(--eds-tertiary-button-hover-text-color);
      }

      @include focus {
        background: var(--eds-tertiary-button-focus-background-color);
        box-shadow: inset 0 0 0 1px var(--eds-tertiary-button-focus-border-color);
        color: var(--eds-tertiary-button-focus-text-color);
      }

      &:active,
      &[aria-pressed="true"],
      &[aria-expanded="true"] {
        background: var(--eds-tertiary-button-active-background-color);
        box-shadow: inset 0 0 0 1px var(--eds-tertiary-button-active-border-color);
        color: var(--eds-tertiary-button-active-text-color);
      }

      @include disabled {
        background: var(--eds-tertiary-button-disabled-background-color);
        box-shadow: inset 0 0 0 1px var(--eds-tertiary-button-disabled-border-color);
        color: var(--eds-tertiary-button-disabled-text-color);
        --eds-icon-color: var(--eds-tertiary-button-disabled-text-color);
      }

    } // tertiary

    /**
    * @class button--alt-tertiary Styles the button to be alternate tertiary button
    */
    @include modifier('alt-tertiary') {
      background: var(--eds-alt-tertiary-button-background-color);
      box-shadow: var(--eds-alt-tertiary-button-box-shadow);
      font-weight: var(--eds-alt-tertiary-button-font-weight);
      color: var(--eds-alt-tertiary-button-text-color);
      --eds-icon-color: var(--eds-tertiary-button-text-color);

      @include hover {
        background: var(--eds-alt-tertiary-button-hover-background-color);
        box-shadow: var(--eds-alt-tertiary-button-box-shadow-hover);
        color: var(--eds-alt-tertiary-button-hover-text-color);
      }

      @include focus {
        background: var(--eds-alt-tertiary-button-focus-background-color);
        box-shadow: var(--eds-alt-tertiary-button-box-shadow-focus);
        color: var(--eds-alt-tertiary-button-focus-text-color);
      }

      &:active,
      &[aria-pressed="true"],
      &[aria-expanded="true"] {
        background: var(--eds-alt-tertiary-button-active-background-color);
        box-shadow: var(--eds-alt-tertiary-button-box-shadow-active);
        color: var(--eds-alt-tertiary-button-active-text-color);
      }

      @include disabled {
        background: var(--eds-alt-tertiary-button-disabled-background-color);
        box-shadow: var(--eds-alt-tertiary-button-box-shadow-disabled);
        color: var(--eds-alt-tertiary-button-disabled-text-color);
        --eds-icon-color: var(--eds-tertiary-button-disabled-text-color);
      }

    } // alt-tertiary

    /**
    * @class button--no-border Modifier class to remove any border from the button
    */
    @include modifier('no-border') {
      box-shadow: none;

      &:hover,
      &:focus,
      &:active,
      &[aria-pressed="true"],
      &[aria-expanded="true"] {
        box-shadow: none;
      }

      @include modifier('app-toggle') {
        box-shadow: none;
        background: transparent;

        &:hover,
        &:focus,
        &:active,
        &[aria-pressed="true"],
        &[aria-expanded="true"] {
          box-shadow: none;
          background: transparent;
        }
      }

    }

    /**
     * @class button--icon-transparent Styles to remove an icon button background color
     */
    @include modifier('icon-transparent') {
      background: transparent;
      border-radius: 0;
      box-shadow: none;
      height: auto;
      transition: none;

      &:hover,
      &:focus,
      &:active,
      &[disabled],
      &[aria-pressed="true"],
      &[aria-expanded="true"] {
        background: transparent;
      }

    }

    /**
     * @class button--icon Styles the button to be a icon button with no text
     */
    @include modifier('icon') {
      border: 0;
      color: var(--eds-button-icon-color);
      padding-left: $eds-button-default-padding-small;
      padding-right: $eds-button-default-padding-small;

      @include button-state('hover') {
        color: var(--eds-button-icon-color-hover);
      }

      @include button-state('pressed') {
        color: var(--eds-button-icon-color-pressed);
      }

      @include button-state('disabled') {
        color: var(--eds-button-icon-color-disabled);
      }

      .button__icon {
        margin-right: 0;
      }

    }

    /**
    * @class button__icon Styles the icon button with text
    */
    @include element('icon') {
      margin-right: $eds-button-icon-margin;
      position: relative;

      /**
      * @class button__icon--color Styles icon color by using fill: currentColor
      */
      @include modifier('color') {
        fill: currentColor;
      }
    }

    /**
    * @class button--dark Styles icon color for dark theme
    */
    @include modifier('dark') {
      color: inherit;
      padding: $eds-button-dark-padding;

      @include button-state('hover') {
        background-color: var(--eds-navigation-item-selected-background-color);
        color: inherit;
      }

      @include button-state('pressed') {
        background-color: var(--eds-navigation-item-selected-background-color);
        box-shadow: $eds-button-dark-box-shadow var(--eds-navigation-selected-border);
        color: inherit;
      }

    }

    /**
    * @class button--dark Styles icon color for dark theme secondary button
    */
    @include modifier('dark-secondary') {
      color: var(--eds-button-icon-color-dark);

      @include button-state('hover') {
        color: var(--eds-button-icon-color-dark-hover);
      }

      @include button-state('pressed') {
        color: var(--eds-button-icon-color-dark-pressed);
      }

      @include button-state('disabled') {
        color: var(--eds-button-icon-color-dark-disabled);
      }

    }

    /**
    * @class button--dark-skinny Styles icon color for dark skinny theme
    */
    @include modifier('dark-skinny') {
      color: inherit;
      padding: $eds-button-dark-skinny-padding;

      &:focus {
        color: inherit;
      }
    }


    /**
    * @class button--full-width DEPRECATED -- Styles the button to take up the entire width of its parent container (use `u--1/1` and `flex--center` instead)
    */
    @include modifier('full-width') {
      justify-content: center;
      padding: $eds-button-default-padding;
      width: 100%;
    }

  } // button

} // eds-button

@include eds-button();
