import { CommunicationsTab as CommunicationsTabProps } from "../../models/CommunicationsTab";
import ServiceNotificationPreferences from "../ServiceNotificationPreferences";

export const CommunicationsTab = (props: CommunicationsTabProps) => {
  return (
    <>
      {props?.communicationPreferences?.map((preference, index) => (
        <ServiceNotificationPreferences key={preference.name || index} {...preference} />
      ))}
    </>
  );
};
