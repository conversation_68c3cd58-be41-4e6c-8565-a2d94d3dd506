/**
*  palette-util.js
*
*  import all dash-cased sass variables from our color palette and convert them to camelCased
*  js vars
*
*   Copyright (c) 2018 by General Electric Company. All rights reserved.
*
*  The copyright to the computer software herein is the property of
*  General Electric Company. The software may be used and/or copied only
*  with the written permission of General Electric Company or in accordance
*  with the terms and conditions stipulated in the agreement/contract
*  under which the software has been supplied.
*
*
*/

import colors from '!!dart-sass-variable-loader!./_palette.scss';

/**
 * Converts a hex-format color to RGB.
 *
 * @param {String} hex - A color in hex format
 * @return {String} - A color in RGB format, e.g '255,255,255'
 */
function hexToRgb(hex) {
  // Expand shorthand form (e.g. "03F") to full form (e.g. "0033FF")
  var shorthandRegex = /^#?([a-f\d])([a-f\d])([a-f\d])$/i;
  hex = hex.replace(shorthandRegex, function(m, r, g, b) {
    return r + r + g + g + b + b;
  });
  var result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? '' + parseInt(result[1], 16) + ',' +
                            parseInt(result[2], 16)+ ',' +
                            parseInt(result[3], 16) : null;
}

export {colors, hexToRgb};
