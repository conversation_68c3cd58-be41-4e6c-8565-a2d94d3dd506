import { render, screen, fireEvent } from '@testing-library/react';
import { EquipmentServiceCardProps } from '../models';
import { EquipmentServiceCard } from '../Component';

// Simple mocks
jest.mock('../../utils', () => ({
  getUserSpecificLink: jest.fn((links) => Array.isArray(links) ? links[0] : links),
  isCSMReadOnly: jest.fn(() => false),
}));

jest.mock('../../utils/commonUtils', () => ({
  shouldShowComponent: jest.fn(() => true),
}));

jest.mock('cx-dle-common-lib', () => ({
  OMNITURE_EVENTS: {
    LINK_CLICK: 'link_click',
  },
  prepareDataAnalyticsAttributes: jest.fn(() => ({})),
}));

jest.mock('html-react-parser', () => jest.fn((html) => html));

// Mock child components
jest.mock('../../EquipmentStatusMessage', () => {
  return function MockEquipmentStatusMessage(props: any) {
    return <div data-testid="equipment-status-message" {...props} />;
  };
});

jest.mock('../../SetupYourFleetPopup', () => {
  return function MockSetupYourFleetPopup(props: any) {
    return <div data-testid="setup-your-fleet-popup" {...props} />;
  };
});

const mockCardData = [
  {
    id: 'SERVICE_TECHNICAL_TRAINING',
    name: 'Equipment service',
    link: [{
      countryCode: 'US',
      redirectLocale: 'en-US',
      href: 'https://test.com',
      text: 'Equipment service',
      anchor: '',
      linktype: '',
      class: 'ico-award-32',
      title: 'Equipment service',
      target: '_blank',
      url: 'https://test.com',
    }],
    alternateLink: [{
      countryCode: 'US',
      redirectLocale: 'en-US',
      href: 'https://alternate.com',
      linktype: '',
      url: 'https://alternate.com',
      anchor: '',
      class: '',
      target: '_blank',
    }],
    title: 'Equipment service',
    description: 'Equipment service description',
    supportedCountries: ['US']
  },
];

const baseProps: EquipmentServiceCardProps = {
  cardData: mockCardData,
  status: 'Closed',
  setupBtn: {
    href: '/' as unknown as URL,
    text: 'Setup Equipment',
    linktype: '',
    url: '/' as unknown as URL,
    anchor: '',
    target: '',
  },
  defaultCountry: 'US',
  hasAccess: true,
  userMailingCountry: 'US',
};

describe('EquipmentServiceCard Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Loading State', () => {
    it('shows loader when loading is true', () => {
      render(<EquipmentServiceCard {...baseProps} loading={true} />);
      expect(screen.getByTestId('equipment-service-card-loading')).toBeInTheDocument();
    });

    it('does not show main content when loading', () => {
      render(<EquipmentServiceCard {...baseProps} loading={true} />);
      expect(screen.queryByText('Equipment service')).not.toBeInTheDocument();
    });
  });

  describe('Basic Rendering', () => {
    it('renders component with card data', () => {
      render(<EquipmentServiceCard {...baseProps} hasAccess={true} />);
      expect(screen.getByText('Equipment service')).toBeInTheDocument();
      expect(screen.getByText('Equipment service description')).toBeInTheDocument();
    });

    it('renders without access', () => {
      render(<EquipmentServiceCard
        {...baseProps}
        hasAccess={false}
        title="Equipment Services"
      />);

      expect(screen.getByText('Equipment Services')).toBeInTheDocument();
      expect(screen.getByTestId('equipment-status-message')).toBeInTheDocument();
    });
  });

  describe('Trademark Replacement', () => {
    it('replaces {TM} with trademark symbol in title and description', () => {
      const cardDataWithTrademark = [{
        ...mockCardData[0],
        title: 'Equipment{TM} Service',
        description: 'Service{TM} description with trademark'
      }];

      render(<EquipmentServiceCard {...baseProps} cardData={cardDataWithTrademark} hasAccess={true} />);

      expect(screen.getByText('Equipment™ Service')).toBeInTheDocument();
      expect(screen.getByText('Service™ description with trademark')).toBeInTheDocument();
    });
  });

  describe('Setup Your Fleet Functionality', () => {
    it('shows setup popup when conditions are met', () => {
      const setupFleetCardData = [{
        ...mockCardData[0],
        link: [{
          ...mockCardData[0].link[0],
          class: 'ico-hospital-32'
        }],
        description: 'Setup {0} of {1} equipment'
      }];

      render(<EquipmentServiceCard
        {...baseProps}
        cardData={setupFleetCardData}
        hasAccess={true}
        isSetupPopUpVisible={true}
        setupAmount={5}
        totalAmount={10}
        popupData={{
          toolTipButtonLabel: 'Got it',
          toolTipDescriptionLabel: 'Setup description',
          toolTipTitleLabel: 'Setup Title'
        }}
      />);

      expect(screen.getByTestId('setup-your-fleet-popup')).toBeInTheDocument();
      expect(screen.getByText('Setup 5 of 10 equipment')).toBeInTheDocument();
    });

    it('does not show setup popup when isSetupPopUpVisible is false', () => {
      const setupFleetCardData = [{
        ...mockCardData[0],
        link: [{
          ...mockCardData[0].link[0],
          class: 'ico-hospital-32'
        }]
      }];

      render(<EquipmentServiceCard
        {...baseProps}
        cardData={setupFleetCardData}
        hasAccess={true}
        isSetupPopUpVisible={false}
      />);

      expect(screen.queryByTestId('setup-your-fleet-popup')).not.toBeInTheDocument();
    });
  });

  describe('Verisound Fleet Access', () => {
    it('uses alternate link for Verisound Fleet when conditions are met', () => {
      const verisoundCardData = [{
        ...mockCardData[0],
        name: 'Log in to Verisound Fleet'
      }];

      render(<EquipmentServiceCard
        {...baseProps}
        cardData={verisoundCardData}
        hasAccess={true}
        hasVerisoundFleetAccess={true}
        isHoldingAccount={false}
      />);

      const link = screen.getByRole('link');
      expect(link).toHaveAttribute('href', 'https://alternate.com');
    });

    it('uses regular link for Verisound Fleet when holding account', () => {
      const verisoundCardData = [{
        ...mockCardData[0],
        name: 'Log in to Verisound Fleet'
      }];

      render(<EquipmentServiceCard
        {...baseProps}
        cardData={verisoundCardData}
        hasAccess={true}
        hasVerisoundFleetAccess={true}
        isHoldingAccount={true}
      />);

      const link = screen.getByRole('link');
      expect(link).toHaveAttribute('href', 'https://test.com');
    });
  });

  describe('Setup Button Functionality', () => {
    it('shows setup button when no access and not pending/in progress', () => {
      render(<EquipmentServiceCard
        {...baseProps}
        hasAccess={false}
        status="Closed"
        setupBtn={{
          href: '/setup' as unknown as URL,
          text: 'Setup Equipment',
          linktype: '',
          url: '/setup' as unknown as URL,
          anchor: '',
          target: '',
        }}
      />);

      expect(screen.getByRole('button', { name: 'Setup Equipment' })).toBeInTheDocument();
    });

    it('does not show setup button when status is pending', () => {
      render(<EquipmentServiceCard
        {...baseProps}
        hasAccess={false}
        status="Pending"
        setupBtn={{
          href: '/setup' as unknown as URL,
          text: 'Setup Equipment',
          linktype: '',
          url: '/setup' as unknown as URL,
          anchor: '',
          target: '',
        }}
      />);

      expect(screen.queryByRole('button', { name: 'Setup Equipment' })).not.toBeInTheDocument();
    });

    it('does not show setup button when status is in progress', () => {
      render(<EquipmentServiceCard
        {...baseProps}
        hasAccess={false}
        status="In Progress"
        setupBtn={{
          href: '/setup' as unknown as URL,
          text: 'Setup Equipment',
          linktype: '',
          url: '/setup' as unknown as URL,
          anchor: '',
          target: '',
        }}
      />);

      expect(screen.queryByRole('button', { name: 'Setup Equipment' })).not.toBeInTheDocument();
    });

    it('handles setup button click when not CSM read-only', () => {
      const mockLocation = { href: '' };
      Object.defineProperty(window, 'location', {
        value: mockLocation,
        writable: true,
      });

      render(<EquipmentServiceCard
        {...baseProps}
        hasAccess={false}
        status="Closed"
        setupBtn={{
          href: '/setup' as unknown as URL,
          text: 'Setup Equipment',
          linktype: '',
          url: '/setup' as unknown as URL,
          anchor: '',
          target: '',
        }}
      />);

      const setupButton = screen.getByRole('button', { name: 'Setup Equipment' });
      fireEvent.click(setupButton);

      expect(mockLocation.href).toBe('/');
    });

    it('calls checkCSMMode when CSM read-only and setup button clicked', () => {
      const { isCSMReadOnly } = require('../../utils');
      isCSMReadOnly.mockReturnValue(true);

      const mockCheckCSMMode = jest.fn();

      render(<EquipmentServiceCard
        {...baseProps}
        hasAccess={false}
        status="Closed"
        checkCSMMode={mockCheckCSMMode}
        setupBtn={{
          href: '/setup' as unknown as URL,
          text: 'Setup Equipment',
          linktype: '',
          url: '/setup' as unknown as URL,
          anchor: '',
          target: '',
        }}
      />);

      const setupButton = screen.getByRole('button', { name: 'Setup Equipment' });
      fireEvent.click(setupButton);

      expect(mockCheckCSMMode).toHaveBeenCalled();
    });
  });

  describe('Country Support and Visibility', () => {
    it('does not render items when shouldShowComponent returns false', () => {
      const { shouldShowComponent } = require('../../utils/commonUtils');
      shouldShowComponent.mockReturnValue(false);

      render(<EquipmentServiceCard {...baseProps} hasAccess={true} />);

      expect(screen.queryByText('Equipment service')).not.toBeInTheDocument();
    });

    it('renders items when shouldShowComponent returns true', () => {
      const { shouldShowComponent } = require('../../utils/commonUtils');
      shouldShowComponent.mockReturnValue(true);

      render(<EquipmentServiceCard {...baseProps} hasAccess={true} />);

      expect(screen.getByText('Equipment service')).toBeInTheDocument();
    });
  });

  describe('CSS Classes and Styling', () => {
    it('applies correct classes when hasAccess is true', () => {
      const { container } = render(<EquipmentServiceCard {...baseProps} hasAccess={true} />);

      const list = container.querySelector('.equipment-service-card__list');
      expect(list).not.toHaveClass('equipment-service-card__list-inline');

      const item = container.querySelector('.equipment-service-card__item');
      expect(item).toBeInTheDocument();
      expect(item).not.toHaveClass('equipment-service-card__item-inline');

      const link = container.querySelector('.equipment-service-card__cta-link');
      expect(link).not.toHaveClass('equipment-service-card__cta-link--inline');
    });

    it('applies correct classes when hasAccess is false', () => {
      const { container } = render(<EquipmentServiceCard {...baseProps} hasAccess={false} />);

      const boxed = container.querySelector('.equipment-service-card__boxed');
      expect(boxed).toBeInTheDocument();

      const list = container.querySelector('.equipment-service-card__list');
      expect(list).toHaveClass('equipment-service-card__list-inline');

      const item = container.querySelector('.equipment-service-card__item-inline');
      expect(item).toBeInTheDocument();

      const link = container.querySelector('.equipment-service-card__cta-link--inline');
      expect(link).toBeInTheDocument();
    });

    it('applies special icon class for tools service', () => {
      const toolsCardData = [{
        ...mockCardData[0],
        link: [{
          ...mockCardData[0].link[0],
          class: 'ico-tools-32'
        }]
      }];

      const { container } = render(<EquipmentServiceCard
        {...baseProps}
        cardData={toolsCardData}
        hasAccess={true}
      />);

      const iconWrap = container.querySelector('.equipment-service-card__img-wrap-request-service__icon');
      expect(iconWrap).toBeInTheDocument();
    });
  });

  describe('Analytics and Link Titles', () => {
    it('uses equipmentServiceLabel for analytics when provided', () => {
      render(<EquipmentServiceCard
        {...baseProps}
        hasAccess={true}
        equipmentServiceLabel="Equipment Service Analytics"
      />);

      // Component should render without errors
      expect(screen.getByText('Equipment service')).toBeInTheDocument();
    });

    it('uses otherServiceLabel for analytics when equipmentServiceLabel not provided', () => {
      render(<EquipmentServiceCard
        {...baseProps}
        hasAccess={true}
        otherServiceLabel="Other Service Analytics"
      />);

      // Component should render without errors
      expect(screen.getByText('Equipment service')).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('handles empty cardData array', () => {
      render(<EquipmentServiceCard {...baseProps} cardData={[]} hasAccess={true} />);

      const list = screen.getByRole('list');
      expect(list).toBeInTheDocument();
      expect(list.children).toHaveLength(0);
    });

    it('handles missing setupBtn', () => {
      render(<EquipmentServiceCard
        {...baseProps}
        hasAccess={false}
        setupBtn={undefined}
      />);

      expect(screen.queryByRole('button')).not.toBeInTheDocument();
    });

    it('handles missing title when no access', () => {
      render(<EquipmentServiceCard
        {...baseProps}
        hasAccess={false}
        title={undefined}
      />);

      expect(screen.queryByRole('heading')).not.toBeInTheDocument();
    });

    it('handles description without setup fleet class', () => {
      const regularCardData = [{
        ...mockCardData[0],
        description: 'Regular description without placeholders'
      }];

      render(<EquipmentServiceCard
        {...baseProps}
        cardData={regularCardData}
        hasAccess={true}
      />);

      expect(screen.getByText('Regular description without placeholders')).toBeInTheDocument();
    });
  });
});

export {};
