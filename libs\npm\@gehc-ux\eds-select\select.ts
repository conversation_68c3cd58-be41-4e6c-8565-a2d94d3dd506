/**
 *  select.ts
 *
 *  Copyright (c) 2017 by General Electric Company. All rights reserved.
 *
 *  The copyright to the computer software herein is the property of
 *  General Electric Company. The software may be used and/or copied only
 *  with the written permission of General Electric Company or in accordance
 *  with the terms and conditions stipulated in the agreement/contract
 *  under which the software has been supplied.
 *
 *  Created by ********* on 10/18/18.
 */

import {
  NgModule, Component, ElementRef, OnChanges, Injector, Input,
  ViewEncapsulation, CUSTOM_ELEMENTS_SCHEMA, SimpleChanges, ViewChild,
  ChangeDetectorRef, OnInit } from '@angular/core';
import { PropType, edsCreateCustomElement } from '@gehc-ux/eds-core/src/app/components/core/property-typing';
import { CommonModule } from '@angular/common';
import { EDSComponent } from '@gehc-ux/eds-core/src/app/components/core/eds-component';
import { NgSelectModule, NgSelectComponent } from '@gehc-ux/ng-select';
import { FormsModule } from '@angular/forms';
import { EDSIconModule } from '@gehc-ux/eds-icon/icon';
const styles = require('./scss/_select.scss');

const componentName = 'eds-select';

/**
 * @name Dropdown
 * @description:
 * The **select** component, also known as a **drop down list**, is a form element that upon interaction opens a list of predetermined value options from which the user to choose. Selection(s) made by the user from this list, associates said value(s) with the form field.
 *
 * @browsers chrome, safari, firefox, ie11, edge
 *
 * @usage
 * For the user to be able to load data onto select, you will need to programmatically pass the data the user will interact with
 * You can pass the data with the following vanilla JavaScript:
 *
 *
 * ```javascript
 * var select = document.querySelector('#select');
 * select.items = [{"value":1,"label":"Option 1","description":"Option 1 Description"},{"value":2,"label":"Option 2","disabled":true,"description":"Option 2 Description"}];
 * ```
 *
 * The following css variables are available for styling :
 *
   CSS Variable | Description
   :----------------|:-------------
    --eds-select-text-color | Color dropdown text
    --eds-select-border-color | Color for dropdown border
    --eds-select-hover-border-color | Color for dropdown border on hover
    --eds-select-border-focused-color | Color for dropdown border on focus
    --eds-select-option-marked-background-color | Color for dropdown background when option marked
    --eds-select-option-selected-text-color | Color for dropdown text when option selected
    --eds-select-option-selected-background-color | Color for dropdown background when option selected
    --eds-select-multiple-value-background-color | Color for dropdown background with multiple values
    --eds-select-multiple-value-border-color | Color for dropdown border with multiple values
    --eds-select-multiple-value-disabled-border-color | Color for dropdown border with multiple values when disabled is true
    --eds-select-option-disabled-color | Color for dropdown option when disabled state is true
    --eds-select-multiple-value-icon-background-color | Color for dropdown background with multiple values and icon
    --eds-select-multiple-value-icon-border-color |  Color for dropdown border with multiple values and icon
    --eds-select-placeholder-color | Color for dropdown placeholder text
    --eds-select-panel-border-color | Color for dropdown panel border
    --eds-select-panel-outer-shadow-color | Color for dropdown panel outer shadow
    --eds-select-header-row-text-color | Color for dropdown header row text
    --eds-select-header-row-border-color | Color for dropdown header row border
    --eds-select-background-color | Color for dropdown background
    --eds-select-disabled-background-color | Color for dropdown background when disable state is true
    --eds-select-disabled-text-color | Color for dropdown text when disabled state is true
    --eds-select-error-background-color | Color for dropdown background when error is true
    --eds-select-error-border-color | Color for dropdown border when error is true
 *
 */

@Component({
  selector: 'eds-select-ng',
  templateUrl: './select.html',
  styleUrls: ['./scss/_select.scss'],
  encapsulation: ViewEncapsulation.ShadowDom
})

/**
 * @class: EDSSelect
 * @description: Angular component for eds-select
 **/
export class EDSSelect extends EDSComponent implements OnChanges, OnInit {

  internalItems = [];

   /**
   * String used for change the icon file name for panel header icon (search/filter/clear)
   */
  panelHeaderIconFilename: string;

  /**
   * Boolean used for filter to placeholder display
   */
  isSearch: boolean = false;

  /**
   * The ng-select component
   */
  @ViewChild('select', {static: true}) select: any;

  @ViewChild('panelHeaderInput') panelHeaderInput: any;

  /**
   * @description items array. Each item can have the following properties:
   * ```javascript
   *  {
   *    "value":2,
   *    "label":"Option 2",
   *    "disabled":true,
   *    "description":"Option 2 Description"
   * }
   * ```
   */
  @PropType('array') @Input() items: any[] = [];

  /**
   * @description placeholder text
   */
  @PropType('string') @Input() placeholder: string;

  /**
   * @description close the dropdown when a value is selected
   */
  @PropType('boolean') @Input() closeOnSelect: boolean = true;

  /**
   * @description whether the dropdown is in error state
   */
  @PropType('boolean') @Input() isError: boolean = false;

  /**
   * @description select marked dropdown item using tab
   */
  @PropType('boolean') @Input() selectOnTab: boolean = false;


  /**
   * @description custom search function implementation.
   */
  @PropType('function') @Input() searchFn: Function;

  /**
   * @description allows to select multiple items
   */
  @PropType('boolean') @Input() isMultiple: boolean = false;

  /**
   * @description Text to be used in first row of panel to clear or not select anything
   */
  @PropType('string') @Input() clearOptionText: string;

   /**
   * @description type of select functionality
   * - default
   * - search
   * - filter
   */
  @PropType('string') @Input() type: string = 'default';
  /**
   * @description clearOptionText will display if false
   * clearOptionText is hidden and users have to select one of the options if true.
   */
  @PropType('boolean') @Input() isRequired: boolean = false;

  /**
   * @description position of the dropdown:
   * - auto
   * - top
   * - bottom
   */
  @PropType('string') @Input() dropdownPosition: string = 'auto';

  /**
   * @description marks first item as focused when opening/filtering
   */
  @PropType('boolean') @Input() markFirst: boolean = true;

  // TODO: Mark as @input when implementing this feature
  /**
   * @description allows to set limit number of selection when [isMultiple]="true"
   */
  maxSelectedItems: number;

  /**
   * @description enables virtual scrolling for better performance (lot of data case)
   */
  @PropType('boolean') @Input() virtualScroll: boolean = false;

  /**
   * @description custom text when search result is empty
   */
  @PropType('string') @Input() notFoundText: string = 'No items found';

  /**
   * @description appends dropdown to any element(e.g. body) using css selector. For correct positioning body should have position:relative
   */
  @PropType('string') @Input() appendTo: string = null;

  /**
   * @description clears search input when item is selected (default true, but default false when closeOnSelect is false)
   */
  clearSearchOnAdd: boolean = true;


  //display or hide custom search dropdown panel
  //Not exposed to public. Used only in search component.
  @PropType('boolean') @Input() searchPanel: boolean = false;

  /**
  * @description forces dropdown to render in a lights-off theme
  */
  @PropType('boolean') @Input() isDarkTheme: boolean = false;

  // display or hide custom search icon
  // not exposed to public. Used only in search component.
  @PropType('boolean') @Input() hideIcon: boolean = false;

  // not exposed to public. Used only in search component to move icon to left side.
  @PropType('boolean') @Input() leftIcon: boolean = false;

  /**
   * @description changes the panel to two column layout with option label in the first column and option description
   * label in the second column. Option description needs to be passed in items array with key 'description' for each option.
   * Ex: { value: 1, label: 'Option', description: 'Option Description' }
   */
  @PropType('boolean') @Input() addDescriptionColumn: boolean = false;

  /**
   * @description custom text for number of items selected
   */
  @PropType('string') @Input() multiSelectedText: string = 'Selected';

  /**
   * @description multi select shown as filter tags when selected
   */
  @PropType('boolean') @Input() isMultiSelectFilterTags: boolean = false;

  /**
   * @description object property to use for selected model (binds to whole object by default)
   */
  @PropType('string') @Input() bindValue: string;

  /**
   * @description object property to use for label
   */
  @PropType('string') @Input() bindLabel: string = 'label';

    /**
   * @description text for the first column in 2 column layout for panel
   */
  @PropType('string') @Input() labelColumnHeaderText: string = 'Name';

    /**
   * @description text for the second column in 2 column layout for panel
   */
  @PropType('string') @Input() descriptionColumnHeaderText: string = 'Description';

  // TODO: Make as input when implementing this feature
  /**
   * @description set the loading state from the outside
   */
  loading: boolean;

  // TODO: Make as input when implementing this feature
  /**
   * @description custom text for loading items
   */
  loadingText: string = 'Loading...';

  /**
   * @description whether the dropdown is opened
   */
  @PropType('boolean') @Input() isOpened: boolean = false;

  /**
   * @description whether the dropdown is disabled
   */
  @PropType('boolean') @Input() isDisabled: boolean;

  /**
   * @description model for the selected value; set it to null or empty array to clear the selection.
   */
  @PropType('object') @Input() selectedValue = null;

   /**
   * @description This input is intended for EDS component development only! To get search behavior, use `type` input with `search` or `filter` value.
   */
  @PropType('boolean') @Input() isTypeInEnabled = false;

  /**
   * @description search input placeholder text
   */
  @PropType('string') @Input() panelHeaderInputPlaceholder: string;

  /**
   * @description Icon used in place of the caret. Two different icons
   * can be used for opened/closed states
   * The following properties are accepted:
   * {
   *  'loadMethod': ,
   *  'filename': ,
   *  'svgString': ,
   *  'svgStyle': ,
   *  'baseSvgPath':
   * }
   * Refer to `eds-icon` for more information on those properties.  Additionally,
   * three properties on this object control the behavior in the select
   * component.  `doToggle` controls whether the icon is changed when the
   * select state is open or closed.  `openFilename` and `closedFilename` are
   * used to specify which file is used in each state.
   */
  @PropType('object') @Input() icon: any = {
    filename: 'ico-caret-down-16',
    svgStyle: { 'width.px': '16', 'height.px': '16'},
    openFilename: 'ico-caret-up-16', // used when select is open
    closedFilename: 'ico-caret-down-16', // used when select is closed
    loadMethod: 'name',
    doToggle: true
  };

  /**
   * @description Allows to have the dropdown
   * The following properties are accepted:
   * {
   *  'loadMethod': ,
   *  'filename': ,
   *  'svgString': ,
   *  'svgStyle': ,
   *  'baseSvgPath':
   * }
   * Refer to `eds-icon` for more information on those properties.
   */
  @PropType('object') @Input() labelIcon: any;

  /**
   * Prevents the "clear" option at the top of the panel from getting
   * styles (italic and grey)
   */
  @PropType('boolean') @Input() preventClearOptionStyling = false;

  /**
   * @event eds-select-add
   * @description fired when item is added while [isMultiple]="true" and outputs added item
   */

  /**
   * @function onAdd
   * @description: fires 'eds-select-add' on ng-select's add event
   */
  onAdd(event) {
    this.fire('eds-select-add', event);
  }

  /**
   * @event eds-select-blur
   * @description fired on select blur
   */

  /**
   * @function onBlur
   * @description: fires 'eds-select-blur' event on ng-select's blur event
   */
  onBlur(event) {
    this.fire('eds-select-blur', event);
  }

  /**
   * @event eds-select-change
   * @description fired on model change and outputs the whole model
   */

  /**
   * @function onChange
   * @description: fires 'eds-select-change' event on ng-select's change event
   */
  onChange(event) {

    if (event) {
      let newValue;

      // clears the selection if the clearable row is present in panel
      if (Array.isArray(event)) { // isMultiple
        if (event.find(x => x.value === 'eds-select-clear')) {
          // we are using non-documented ng-select function that might change in the future
          this.select.itemsList.clearSelected(true);
          this.selectedValue = null;
          this.fire('eds-select-clear');
          newValue = null;
          this.close();
        } else {
          newValue = event;
        }
      } else { // single
        if (event.value === 'eds-select-clear') {
          // we are using non-documented ng-select function that might change in the future
          this.select.itemsList.clearSelected(true);
          this.selectedValue = null;
          this.fire('eds-select-clear');
          newValue = null;
        } else {
          newValue = event;
        }
      }
  
      if (newValue === null) {
        this.fire('eds-select-change', newValue);
      } else {
        if (!newValue.isTrusted) {
          this.fire('eds-select-change', newValue);
        }
      }
    } else {
      this.fire('eds-select-clear');
      this.fire('eds-select-change', null);
    }

    this.isSearch = false;
    this.cd.detectChanges(); // Added for IE + Edge
  }

  /**
   * @event eds-select-close
   * @description fired on select dropdown close
   */

  /**
   * @function onOpen
   * @description: fires 'eds-select-open' event on ng-select's open event
   */
  onOpen() {
    
    /**
     * setTimeout placed to interact appropriately with icons and avoid
     * the behavior of the select staying open due to behavior of underlying
     * technology. There was an edge case where if icon is clicked when
     * `isOpened = true`, the component would stay open due to the following
     * cycle onClose --> onBlur --> onIconClick
     * Since onClose gets fired first and the state closes, when
     * onIconClick evaluates `isOpened`, the dropdown is reopened (thus the loop)
     */
    setTimeout(() => {
      this.isOpened = true;
      this.setIconOpen();
      this.fire('eds-select-open');
    }, 150);
  }

  /**
   * @function onClose
   * @description: fires 'eds-select-close' event on ng-select's close event
   */
  onClose() {
    /**
     * setTimeout placed to interact appropriately with icons and avoid
     * the behavior of the select staying open due to behavior of underlying
     * technology. There was an edge case where if icon is clicked when
     * `isOpened = true`, the component would stay open due to the following
     * cycle onClose --> onBlur --> onIconClick
     * Since onClose gets fired first and the state closes, when
     * onIconClick evaluates `isOpened`, the dropdown is reopened (thus the loop)
     */
    setTimeout(() => {
      this.isOpened = false;
      this.setIconClosed();
      if (this.type === 'search') {
        this.panelHeaderIconFilename = "ico-search-16";
      }
      if (this.type === 'filter') {
        this.panelHeaderIconFilename = "ico-filter-16";
      }
      this.fire('eds-select-close');
      this.cd.detectChanges(); // Added for IE + Edge
    }, 150);
    this.isSearch = false;
  }

  /**
   * @event eds-select-clear
   * @description fired on clear icon click
   */

  /**
   * @function onClear
   * @description: fires 'eds-select-clear' event on ng-select's clear event
   */
  onClear() {
    this.fire('eds-select-clear');
    this.cd.detectChanges(); // Added for IE + Edge
  }

  /**
   * @function returnLabel
   * @description: returns label based on the bindLabel input
   */
  returnLabel(item) {
    return item[this.bindLabel];
  }

   /**
   * @function onPanelHeaderInputChange
   * @description: filters the options and toggles the search/filter and clear icons based on if input is empty or not
   */
  onPanelHeaderInputChange(event) {
    this.select.filter(event.target.value);
    if (this.type === 'search') {
      this.panelHeaderIconFilename = event.target.value ? "ico-close-16" : "ico-search-16";
    }
    if (this.type === 'filter') {
      this.panelHeaderIconFilename = event.target.value ? "ico-close-16" : "ico-filter-16";
    }
    this.cd.detectChanges(); // Added for IE + Edge
  }

  /**
   * @event eds-select-focus
   * @description fired on select focus
   */

  /**
   * @function onFocus
   * @description: fires 'eds-select-focus' event on ng-select's focus event
   */
  onFocus() {
    this.fire('eds-select-focus');
  }

    /**
   * eds-select-search
   * @description fired while typing search term
   */

  /**
   * @function onSearch
   * @description: fires 'eds-select-search' event when user starts typing into the search field
   */
  onSearch(event) {
    this.fire('eds-select-search', event);
    if (event.term) {
      this.isSearch = true;
    } else {
      this.isSearch = false;
    }
  }

  /**
   * @event eds-select-open
   * @description fired on select dropdown open
   */

  /**
   * @event eds-select-remove
   * @description fired when item is removed while [isMultiple]="true"
   */

  /**
   * @function onRemove
   * @description: Fires 'eds-select-remove' event on ng-select's remove event
   */
  onRemove(event) {
    this.fire('eds-select-remove', event);
    this.cd.detectChanges(); // Added for IE + Edge
  }

  /**
   * @event eds-select-scroll
   * @description fired when scrolled and provides the start and end index of the currently available items
   */

  /**
   * @function onScroll
   * @description: fires 'eds-select-scroll' event on ng-select's scroll event
   */
  onScroll(event) {
    this.fire('eds-select-scroll', event);
  }

  /**
   * @event eds-select-scroll-to-end
   * @description fired when scrolled to the end of items
   */

  /**
   * @function onScrollToEnd
   * @description: fires 'eds-select-scroll-to-end' event on ng-select's scrollToEnd event
   */
  onScrollToEnd() {
    this.fire('eds-select-scroll-to-end');
  }

  constructor(private element: ElementRef, readonly cd: ChangeDetectorRef) {
    super(element, componentName);

    // ensure exposed method retains the right context
    this.open = this.open.bind(this);
    this.close = this.close.bind(this);
    this.focus = this.focus.bind(this);
  }

   /**
   * @function clearItemSetup
   * @description: This function is called to add or remove the clear item in panel based on isRequired and clearOptionText inputs
   * @param clearOptionText
   */
  clearItemSetup(clearOptionTextValue) {
    const clearObject = clearOptionTextValue ? { value: 'eds-select-clear', [this.bindLabel]: clearOptionTextValue } : { value: 'eds-select-clear', [this.bindLabel]: '- - -' };
      if (this.internalItems) {
        const index = this.internalItems.findIndex(x => x.value === 'eds-select-clear');
        const headerIndex = this.internalItems.findIndex(x => x.value === 'eds-select-header');
        let headerObject = {};
        if (index !== -1) {
          this.internalItems.splice(index, 1);
        }
        if (headerIndex !== -1) {
          headerObject = this.internalItems[headerIndex];
          this.internalItems.splice(headerIndex, 1);
        }
        this.internalItems = Object.keys(headerObject).length === 0 && headerObject.constructor === Object ? [clearObject, ...this.internalItems] : [headerObject, clearObject, ...this.internalItems];
      }
  }

     /**
   * @function updateTwoColumnLayoutItems
   * @description: This function is called to add or remove the clear item in panel based on isRequired and placeholder inputs
   * @param inputName - string - options: addDescriptionColumn | descriptionLabelText | optionLabelText
   * @param inputValue - boolean when inputName is optionLabelText or string when inputName is descriptionLabelText or optionLabelText
   */
  updateTwoColumnLayoutItems(inputName, inputValue) {
    let headerObject;
    switch (inputName) {
      case 'addDescriptionColumn':
      headerObject = inputValue ? { value: 'eds-select-header', [this.bindLabel]: this.labelColumnHeaderText, description: this.descriptionColumnHeaderText, disabled: true } : null;
      break;
      case 'descriptionLabelText':
      headerObject = inputValue ? { value: 'eds-select-header', [this.bindLabel]: this.labelColumnHeaderText, description: inputValue, disabled: true } : null;
      break;
      case 'optionLabelText':
      headerObject = inputValue ? { value: 'eds-select-header', [this.bindLabel]: inputValue, description: this.descriptionColumnHeaderText, disabled: true } : null;
      break;
      default:
      headerObject = this.addDescriptionColumn ? { value: 'eds-select-header', [this.bindLabel]: this.labelColumnHeaderText, description: this.descriptionColumnHeaderText, disabled: true } : null;
      break;
    }

    if (this.internalItems) {
      const index = this.internalItems.findIndex(x => x.value === 'eds-select-header');
      if (index !== -1) {
        this.internalItems.splice(index, 1);
      }
      this.internalItems = headerObject ? [headerObject, ...this.internalItems] : [...this.internalItems];
    }

  }

 /**
   * @function itemsSetup
   * @description: This function is called to to update the items object when items are changed.
   * @param items
   */
  itemsSetup(items) {
    let clearObject, headerObject;
    if (this.internalItems) {
      clearObject = this.internalItems.find(x => x.value === 'eds-select-clear');
      headerObject = this.internalItems.find(x => x.value === 'eds-select-header');
    }

    const currentItems = items ? items : [];
    if (clearObject && headerObject) {
      this.internalItems = [headerObject, clearObject, ...currentItems];
    } else if (clearObject) {
      this.internalItems = [clearObject, ...currentItems];
    } else if (headerObject) {
      this.internalItems = [headerObject, ...currentItems];
    } else {
      this.internalItems = [...currentItems];
    }
  }

   /**
   * @function requiredItemSetup
   * @description: This function is called to to update the items when isRequired input is changed.
   * @param isRequired
   */
  requiredItemSetup(isRequired) {
    if (!isRequired) {
      this.clearItemSetup(this.clearOptionText);
    } else {
      const index = this.internalItems.findIndex(x => x.value === 'eds-select-clear');
      if (index !== -1) {
        this.internalItems.splice(index, 1);
      }
      this.internalItems = [...this.internalItems];
    }
  }


   /**
   * ngOnInit
   * @description: This is a life cycle hook called by Angular when the component is initialized.
   */
  ngOnInit() {
    if (this.type === 'search') {
      this.panelHeaderIconFilename = "ico-search-16";
    }
    if (this.type === 'filter') {
      this.panelHeaderIconFilename = "ico-filter-16";
    }
    this.updateTwoColumnLayoutItems('addDescriptionColumn', this.addDescriptionColumn);
    this.requiredItemSetup(this.isRequired);
    this.itemsSetup(this.items);
  }

   /**
   * ngOnChanges
   * @description: This is a life cycle hook called by Angular when a bound property changes.
   */
  ngOnChanges(changes: SimpleChanges) {
    if (changes.isOpened) {
      changes.isOpened.currentValue ? this.open() : this.close();
    }
    if (changes.isDisabled) {
      this.select.setDisabledState(changes.isDisabled.currentValue);
      this.setIconColorByIsDisabledState();
    }

    if (changes.isMultiple) {
      this.closeOnSelect = changes.isMultiple.currentValue ? false : true;
    }

    if (changes.clearOptionText) {
      this.clearItemSetup(changes.clearOptionText.currentValue);
    }

    if (changes.isRequired) {
        this.requiredItemSetup(changes.isRequired.currentValue);
    }

    if (changes.appendTo && changes.appendTo.currentValue) {
      const style = document.createElement('style');
      style.innerHTML = styles.default;
      const bindParent = document.querySelector(changes.appendTo.currentValue) ? document.querySelector(changes.appendTo.currentValue) : null ;
      if (bindParent) bindParent.appendChild(style);
    }

    if (changes.bindLabel) {
      this.requiredItemSetup(this.isRequired);
      this.clearItemSetup(this.clearOptionText);
  }

    if (changes.addDescriptionColumn) {
      this.updateTwoColumnLayoutItems('addDescriptionColumn', changes.addDescriptionColumn.currentValue);
    }

    if (changes.labelColumnHeaderText) {
      this.updateTwoColumnLayoutItems('optionLabelText', changes.labelColumnHeaderText.currentValue);
    }

    if (changes.descriptionColumnHeaderText) {
      this.updateTwoColumnLayoutItems('descriptionLabelText', changes.descriptionColumnHeaderText.currentValue);
    }

    if (changes.items) {
      this.itemsSetup(changes.items.currentValue);
    }

    if (changes.selectedValue) {
      if (!changes.selectedValue.currentValue || changes.selectedValue.currentValue.length  === 0) {
        this.onChange({value: 'eds-select-clear'}); // reset dropdown
      }
      setTimeout(() => {
        // stack it to allow ng to update asap
        this.select._cd.detectChanges();
      });
    }

    if (changes.type) {
      if (changes.type.currentValue === 'search') {
        this.panelHeaderIconFilename = "ico-search-16";
        this.panelHeaderInputPlaceholder =  !this.panelHeaderInputPlaceholder || this.panelHeaderInputPlaceholder === 'Filter By' ? "Search" : this.panelHeaderInputPlaceholder;
      }
      if (changes.type.currentValue === 'filter') {
        this.panelHeaderIconFilename = "ico-filter-16";
        this.panelHeaderInputPlaceholder =  !this.panelHeaderInputPlaceholder || this.panelHeaderInputPlaceholder === 'Search' ? "Filter By" : this.panelHeaderInputPlaceholder;
      }
    }

    if (changes.panelHeaderInputPlaceholder && !changes.panelHeaderInputPlaceholder.currentValue) {
      if (this.type === 'search') {
        this.panelHeaderInputPlaceholder = "Search";
      }
      if (this.type === 'filter') {
        this.panelHeaderInputPlaceholder = "Filter By";
      }
    }

    this.cd.detectChanges(); // Added for IE + Edge
  }

  /**
   * @function onPanelHeaderIconCLick
   * @description: clears the search input and sets the icon back to search/filter icon
   */
  onPanelHeaderIconCLick($event) {
    this.panelHeaderInput.nativeElement.value = '';
    this.select.filter('');
    if (this.type === 'search') {
      this.panelHeaderIconFilename = "ico-search-16";
    }
    if (this.type === 'filter') {
      this.panelHeaderIconFilename = "ico-filter-16";
    }
    this.cd.detectChanges(); // Added for IE + Edge
  }

  /**
   * @description: opens the select
   **/
  @Input() open() {
    this.select.open();
    this.setIconOpen();
    this.isOpened = true;
  }

  /**
   * @description: closes the select
   **/
  @Input() close() {
    this.select.close();
    this.setIconClosed();
    this.isOpened = false;
  }

  /**
   * @description: focuses on the select
   **/
  @Input() focus() {
    this.select.focus();
    this.onFocus();
  }

  // opens/closes if icon is clicked. Not exposed to public. Used only in search component.
  @Input()  onIconClick(event) {
    if (this.isDisabled) { return 0; }
    if (this.isOpened) {
      this.close()
    } else {
      this.open();
    }
    this.fire('eds-select-icon-click', event);
  }

  /**
   * @function onClearIconClick
   * @description: handles click event on clear icon to reset the searched text value.
   * @param $event - event fired from dropdown when clear icon is clicked
   */
  onClearIconClick(event) {
    if (this.isDisabled) { return 0; }
    this.select.itemsList.clearSelected(true);
    //fire clear event
    this.onClear();
    this.fire('eds-select-change', {});
  }

  /**
   * handles icon state for open
   */
  private setIconOpen() {
    if (this.icon.doToggle) {
      this.icon.filename = this.icon.openFilename;
    }
  }

  /**
   * handles icon state for closed
   */
  private setIconClosed() {
    if (this.icon.doToggle) {
      this.icon.filename = this.icon.closedFilename;
    }
  }

  /**
   * sets the fill color of the icon based on the state of `isDisabled` property
   */
  private setIconColorByIsDisabledState() {
    const iconSvgStyle = this.icon.svgStyle || {};
    if (this.isDisabled) {
      this.icon.svgStyle = Object.assign({}, iconSvgStyle, {fill: 'var(--eds-textbox-disabled-text-color)'});
    } else {
      this.icon.svgStyle = Object.assign({}, iconSvgStyle, {fill: 'var(--eds-icon-color)'});
    }
  }

  /**
   * Tests whether this value holds something
   * @param val
   */
  isValueEmpty(val, isMultiple?) {

    let result = !val;

    if (val === null || typeof val === 'undefined') {
      result = true;
    } else if (Array.isArray(val)) {
      result = !val.length;
    } else if (typeof val === 'object') {
      result = !Object.keys(val).length;
    }

    return result;
  }
}

@NgModule({
  imports: [CommonModule, NgSelectModule, FormsModule, EDSIconModule],
  exports: [EDSSelect],
  entryComponents: [EDSSelect],
  declarations: [EDSSelect],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})

export class EDSSelectModule {
  constructor(private injector: Injector) {
    const el = edsCreateCustomElement(EDSSelect, { injector: this.injector });
    if (!customElements.get(componentName)) {
      customElements.define(componentName, el);
    }
  }
}
